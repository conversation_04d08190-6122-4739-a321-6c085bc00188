import React, { forwardRef, useImperativeHandle, useState } from "react";
import { useColor } from "./contexts/ColorContext";

const Width = 100;

interface CellProps {
  i: number;
  j: number;
  size: number;
}

interface CellState {
  color: string;
  power: string;
  markNumber: number; // 0 = not marked, 1-4 = marked with number
}

const Cell = forwardRef<{ getState: () => CellState }, CellProps>(({ i, j, size }, ref) => {
  const { activeColor } = useColor();
  const [color, setColor] = useState("white");
  const [power, setPower] = useState("");
  const [markNumber, setMarkNumber] = useState(0);
  var index = i * size + j + 1;

  useImperativeHandle(ref, () => ({
    getState: () => ({
      color,
      power,
      markNumber
    })
  }));

  const handleClick = () => {
    if (activeColor === "A" || activeColor === "B" || activeColor === "C" || activeColor === "D") {
      if (color === "white" || color === "gray") {
        alert("Power symbols are not allowed on white or gray cells");
        return;
      }
      setPower(power === activeColor ? "" : activeColor);  // Toggle power
    } else if (activeColor === "1" || activeColor === "2" || activeColor === "3" || activeColor === "4") {
      if (color === "white" || color === "gray") {
        alert("Number marks are not allowed on white or gray cells");
        return;
      }
      const num = parseInt(activeColor);
      setMarkNumber(markNumber === num ? 0 : num);  // Toggle mark number
    } else {
      if (activeColor === "white" || activeColor === "gray") {
        setPower("");
        setMarkNumber(0);
      }
      setColor(color === activeColor ? "white" : activeColor);  // Toggle color
    }
  };

  const displayText = markNumber > 0 ? (
    <span>
      <span style={{ fontSize: "24px" }}>{index}</span>
      <span style={{ fontSize: "48px" }}>{markNumber}</span>
      <span style={{ fontSize: "24px" }}>{power}</span>
    </span>
  ) : (
    `${index}${power}`
  );

  return (
    <div
      style={{
        width: Width,
        height: Width,
        border: "1px solid black",
        position: "absolute",
        left: j * Width,
        top: i * Width,
        backgroundColor: color,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        color: "black",
        fontSize: markNumber > 0 ? "36px" : "24px", // Larger font size when marked
        cursor: "pointer"
      }}
      onClick={handleClick}
    >
      {displayText}
    </div>
  );
});

export default Cell;
