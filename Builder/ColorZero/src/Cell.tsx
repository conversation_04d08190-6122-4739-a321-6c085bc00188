import React, { forwardRef, useImperativeHandle, useState } from "react";
import { useColor } from "./contexts/ColorContext";

const Width = 100;

interface CellProps {
  i: number;
  j: number;
  size: number;
}

interface CellState {
  color: string;
  power: string;
  marked: boolean;
}

const Cell = forwardRef<{ getState: () => CellState }, CellProps>(({ i, j, size }, ref) => {
  const { activeColor } = useColor();
  const [color, setColor] = useState("white");
  const [power, setPower] = useState("");
  const [marked, setMarked] = useState(false);
  var index = i * size + j + 1;
  
  useImperativeHandle(ref, () => ({
    getState: () => ({
      color,
      power,
      marked
    })
  }));

  const handleClick = () => {
    if (activeColor === "A" || activeColor === "B" || activeColor === "C" || activeColor === "D") {
      if (color === "white" || color === "gray") {
        alert("Power symbols are not allowed on white or gray cells");
        return;
      }
      setPower(power === activeColor ? "" : activeColor);  // Toggle power
    } else if (activeColor === ".") {
      if (color === "white" || color === "gray") {
        alert("Marked dots are not allowed on white or gray cells");
        return;
      }
      setMarked(!marked);  // Toggle marked state
    } else {
      if (activeColor === "white" || activeColor === "gray") {
        setPower("");
        setMarked(false);
      }
      setColor(color === activeColor ? "white" : activeColor);  // Toggle color
    }
  };

  const displayText = marked ? (
    <span>
      <span style={{ fontSize: "24px" }}>{index}</span>
      <span style={{ fontSize: "48px" }}>&bull;</span>
      <span style={{ fontSize: "24px" }}>{power}</span>
    </span>
  ) : (
    `${index}${power}`
  );

  return (
    <div
      style={{
        width: Width,
        height: Width,
        border: "1px solid black",
        position: "absolute",
        left: j * Width,
        top: i * Width,
        backgroundColor: color,
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        color: "black",
        fontSize: marked ? "36px" : "24px", // Larger font size when marked
        cursor: "pointer"
      }}
      onClick={handleClick}
    >
      {displayText}
    </div>
  );
});

export default Cell;
