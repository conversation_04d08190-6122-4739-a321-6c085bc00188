import React, { useRef } from "react";
import Cell from "./Cell";
import { useColor } from "./contexts/ColorContext";
import { getAllSolutions } from "./solution";

const COLORS = [
  "white",
  "#ff0000",
  "#00ff00",
  "#0000ff",
  "#ffff00",
  "#00ffff",
  "#ff00ff",
  "gray",
];

const colorButtonStyle = (
  color: string,
  isActive: boolean
): React.CSSProperties => ({
  backgroundColor: color,
  width: "50px",
  height: "50px",
  border: isActive ? "3px solid black" : "1px solid gray",
});

const powerButtonStyle = (
  power: string,
  isActive: boolean
): React.CSSProperties => ({
  backgroundColor: "white",
  width: "50px",
  height: "50px",
  border: isActive ? "3px solid black" : "1px solid gray",
  fontSize: "24px",
  display: "flex",
  alignItems: "center",
  justifyContent: "center",
});

interface CellState {
  color: string;
  power: string;
  marked: boolean;
}

const Board = ({ size: initialSize = 4 }) => {
  const { activeColor, setActiveColor } = useColor();
  const cellRefs = useRef<{ [key: string]: { getState: () => CellState } }>({});
  const [output, setOutput] = React.useState("");
  const [name, setName] = React.useState("1");
  const [size, setSize] = React.useState(initialSize);
  const [solutions, setSolutions] = React.useState<number[][][]>([]);
  const [executionTime, setExecutionTime] = React.useState<number | null>(null);

  const findConnectedCells = (color: string): number[][] => {
    // Create a map of cell positions with this color
    const colorCells: { [key: string]: boolean } = {};
    const visited: { [key: string]: boolean } = {};

    // Fill the map
    for (let i = 0; i < size; i++) {
      for (let j = 0; j < size; j++) {
        const key = `${i}-${j}`;
        const cellState = cellRefs.current[key]?.getState();
        if (cellState && cellState.color === color) {
          colorCells[key] = true;
        }
      }
    }

    const getConnectedGroup = (i: number, j: number): number[] => {
      const group: number[] = [];
      const markedCells: number[] = [];
      const stack: [number, number][] = [[i, j]];

      while (stack.length > 0) {
        const [currI, currJ] = stack.pop()!;
        const key = `${currI}-${currJ}`;

        if (visited[key]) continue;
        visited[key] = true;

        if (colorCells[key]) {
          const index = currI * size + currJ + 1;
          const cellState = cellRefs.current[key]?.getState();

          // If cell is marked with ".", add to markedCells, otherwise add to regular group
          if (cellState && cellState.marked) {
            markedCells.push(index);
          } else {
            group.push(index);
          }

          // Check adjacent cells (up, down, left, right)
          const adjacent = [
            [currI - 1, currJ],
            [currI + 1, currJ],
            [currI, currJ - 1],
            [currI, currJ + 1],
          ];

          for (const [nextI, nextJ] of adjacent) {
            if (nextI >= 0 && nextI < size && nextJ >= 0 && nextJ < size) {
              const nextKey = `${nextI}-${nextJ}`;
              if (colorCells[nextKey] && !visited[nextKey]) {
                stack.push([nextI, nextJ]);
              }
            }
          }
        }
      }

      // Combine arrays with marked cells first
      return [...markedCells, ...group];
    };

    const groups: number[][] = [];

    // Find all connected components
    for (let i = 0; i < size; i++) {
      for (let j = 0; j < size; j++) {
        const key = `${i}-${j}`;
        if (colorCells[key] && !visited[key]) {
          const group = getConnectedGroup(i, j);
          if (group.length > 0) {
            groups.push(group);
          }
        }
      }
    }

    return groups;
  };

  const handleExport = () => {
    const bannedCells: number[] = [];
    const positivePowerCells: number[] = [];
    const negativePowerCells: number[] = [];
    const aliveGroups: number[][] = [];

    // Get banned and power cells
    for (let i = 0; i < size; i++) {
      for (let j = 0; j < size; j++) {
        const key = `${i}-${j}`;
        const cellState = cellRefs.current[key]?.getState();
        if (cellState) {
          const index = i * size + j + 1;

          if (cellState.color === "gray") {
            bannedCells.push(index);
          }
          if (cellState.power === "A") {
            positivePowerCells.push(index);
          }
          if (cellState.power === "B") {
            negativePowerCells.push(index);
          }
        }
      }
    }

    // Get connected components for each color except white and gray
    let isValid = true;
    for (const color of COLORS) {
      if (color !== "white" && color !== "gray") {
        const groups = findConnectedCells(color);
        // Check if each group has exactly one marked cell and at least one A and one B
        for (const group of groups) {
          const markedCells = group.filter((index) => {
            const i = Math.floor((index - 1) / size);
            const j = (index - 1) % size;
            const key = `${i}-${j}`;
            const cellState = cellRefs.current[key]?.getState();
            return cellState?.marked;
          });

          if (markedCells.length !== 1) {
            alert(`Invalid group: each connected group must have exactly one marked cell`);
            isValid = false;
            break;
          }
        }
        if (!isValid) break;
        aliveGroups.push(...groups);
      }
    }

    if (!isValid) {
      return null;
    }

    // Sort aliveGroups by length in descending order
    aliveGroups.sort((a, b) => b.length - a.length);

    // Create output object starting with "n" field
    const output: any = {
      n: name,
      a: aliveGroups,
    };

    // Only add non-empty arrays to output
    if (bannedCells.length > 0) {
      output["b"] = bannedCells;
    }
    if (positivePowerCells.length > 0) {
      output["A"] = positivePowerCells;
    }
    if (negativePowerCells.length > 0) {
      output["B"] = negativePowerCells;
    }

    setOutput(JSON.stringify(output));
    return output;
  };

  const handleSolve = () => {
    const gameDef = handleExport();
    if (gameDef == null) {
      return;
    }

    const startTime = performance.now();
    const result = getAllSolutions(size, gameDef);
    const endTime = performance.now();
    const executionTime = endTime - startTime;

    setExecutionTime(executionTime);
    setSolutions(result || []);
  };

  const cells = [];
  for (let i = 0; i < size; i++) {
    for (let j = 0; j < size; j++) {
      cells.push(
        <Cell
          i={i}
          j={j}
          size={size}
          key={`${i}-${j}`}
          ref={(ref) => {
            if (ref) {
              cellRefs.current[`${i}-${j}`] = ref;
            }
          }}
        />
      );
    }
  }

  return (
    <div style={{ display: "flex", gap: "20px", padding: "20px" }}>
      {/* Main board area */}
      <div
        style={{
          flex: "1",
          backgroundColor: "white",
          padding: "20px",
          borderRadius: "8px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        }}
      >
        <div style={{ position: "relative" }}>
          {/* Size input above the board */}
          <div
            style={{
              marginBottom: "20px",
              display: "flex",
              justifyContent: "center",
            }}
          >
            <input
              type="number"
              value={size}
              onChange={(e) => setSize(parseInt(e.target.value))}
              style={{
                width: "50px",
                height: "50px",
                textAlign: "center",
                fontSize: "24px",
              }}
            />
          </div>

          <div
            style={{
              position: "relative",
              width: size * 100,
              height: size * 100,
            }}
          >
            {cells}
          </div>

          <div
            className="board-footer"
            style={{
              marginTop: "20px",
              display: "flex",
              flexDirection: "column",
              gap: "10px",
              alignItems: "flex-start",
            }}
          >
            {/* Name input */}
            <div style={{ display: "flex", gap: "10px" }}>
              <input
                type="text"
                value={name}
                onChange={(e) => setName(e.target.value)}
                style={{
                  width: "50px",
                  height: "50px",
                  textAlign: "center",
                  fontSize: "24px",
                }}
              />
            </div>

            {/* Colors */}
            <div style={{ display: "flex", gap: "10px" }}>
              {COLORS.map((color) => (
                <button
                  key={color}
                  onClick={() => setActiveColor(color)}
                  style={colorButtonStyle(color, activeColor === color)}
                />
              ))}
            </div>

            {/* Powers and dot */}
            <div style={{ display: "flex", gap: "10px" }}>
              {["A", "B"].map((power) => (
                <button
                  key={power}
                  onClick={() => setActiveColor(power)}
                  style={powerButtonStyle(power, activeColor === power)}
                >
                  {power}
                </button>
              ))}
              <button
                onClick={() => setActiveColor(".")}
                style={{
                  ...powerButtonStyle(".", activeColor === "."),
                  fontSize: "36px",
                  lineHeight: "36px",
                }}
              >
                &bull;
              </button>
            </div>

            {/* Export and Solve buttons */}
            <div style={{ display: "flex", gap: "10px" }}>
              <button
                onClick={handleExport}
                style={{
                  ...powerButtonStyle("export", false),
                  width: "auto",
                  padding: "0 20px",
                }}
              >
                Export
              </button>
              <button
                onClick={handleSolve}
                style={{
                  ...powerButtonStyle("solve", false),
                  width: "auto",
                  padding: "0 20px",
                }}
              >
                Solve
              </button>
            </div>
          </div>

          {output && (
            <textarea
              value={output}
              readOnly
              style={{
                width: "100%",
                height: "200px",
                marginTop: "20px",
                fontFamily: "monospace",
                padding: "10px",
              }}
            />
          )}
        </div>
      </div>

      {/* Solutions panel */}
      <div
        style={{
          borderLeft: "1px solid #eee",
          padding: "20px",
          height: "100vh",
          overflowY: "auto",
          position: "sticky",
          top: 0,
          backgroundColor: "white",
          borderRadius: "8px",
          boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
        }}
      >
        <h3 style={{ marginTop: 0 }}>Solutions Found: {solutions.length}</h3>
        {executionTime !== null && (
          <div
            style={{
              marginBottom: "20px",
              color: "#666",
              fontSize: "14px",
            }}
          >
            Solution found in {executionTime.toFixed(2)}ms
          </div>
        )}
        <div
          style={{
            fontFamily: "monospace",
            whiteSpace: "pre",
          }}
        >
          {solutions.map((solution, index) => {
            // Create a map of position to cell state
            const cellStates = new Map();
            for (let i = 0; i < size; i++) {
              for (let j = 0; j < size; j++) {
                const cellRef = cellRefs.current[`${i}-${j}`];
                if (cellRef) {
                  const state = cellRef.getState();
                  cellStates.set(`${i}-${j}`, state);
                }
              }
            }

            // Create a map of number to its cell state
            const numberStates = new Map();
            for (let i = 0; i < size; i++) {
              for (let j = 0; j < size; j++) {
                const index = i * size + j + 1;
                const state = cellRefs.current[`${i}-${j}`]?.getState();
                if (state) {
                  numberStates.set(index, state);
                }
              }
            }

            return (
              <div key={index} style={{ marginBottom: "20px" }}>
                <div>Solution {index + 1}:</div>
                {solution.map((row, i) => (
                  <div key={i}>
                    {row.map((cell, j) => {
                      const state = cellStates.get(`${i}-${j}`);
                      const index = i * size + j + 1;
                      const numberState = numberStates.get(cell > 0 ? cell : index);
                      let displayValue;
                      
                      if (cell === -1) {
                        displayValue = "  X  ";
                      } else if (state?.marked) {
                        displayValue = (
                          <span>
                            <span style={{ 
                              fontSize: "24px",
                              color: numberStates.get(index)?.color || "black"
                            }}>{index}</span>
                            <span style={{ fontSize: "24px" }}>&bull;</span>
                            <span style={{ fontSize: "24px" }}>{state.power}</span>
                          </span>
                        );
                      } else {
                        displayValue = `${index}${state?.power || ''}`;
                      }

                      const markedCell = solution.some((r, ri) => 
                        r.some((c, ci) => c === cell && 
                          cellStates.get(`${ri}-${ci}`)?.marked)
                      );
                      
                      return (
                        <span 
                          key={j} 
                          style={{ 
                            display: "inline-block",
                            width: "100px",
                            textAlign: "center",
                            color: cell === -1 ? "gray" : 
                                   numberState?.color || "black",
                            fontWeight: cell > 0 ? "bold" : "normal",
                            fontSize: state?.marked ? "36px" : "24px",
                            backgroundColor: state?.color === "white" ? "#f0f0f0" : "transparent"
                          }}
                        >
                          {displayValue}
                        </span>
                      );
                    })}
                  </div>
                ))}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default Board;
