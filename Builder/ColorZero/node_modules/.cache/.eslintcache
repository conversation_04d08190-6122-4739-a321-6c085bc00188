[{"/Users/<USER>/LightThem/Builder/builder/src/index.tsx": "1", "/Users/<USER>/LightThem/Builder/builder/src/reportWebVitals.ts": "2", "/Users/<USER>/LightThem/Builder/builder/src/App.tsx": "3", "/Users/<USER>/LightThem/Builder/builder/src/Board.tsx": "4", "/Users/<USER>/LightThem/Builder/builder/src/Cell.tsx": "5", "/Users/<USER>/LightThem/Builder/builder/src/contexts/ColorContext.tsx": "6", "/Users/<USER>/LightThem/Builder/builder/src/solution.ts": "7", "/Users/<USER>/Puzzles/Builder/ColorZero/src/index.tsx": "8", "/Users/<USER>/Puzzles/Builder/ColorZero/src/reportWebVitals.ts": "9", "/Users/<USER>/Puzzles/Builder/ColorZero/src/App.tsx": "10", "/Users/<USER>/Puzzles/Builder/ColorZero/src/Board.tsx": "11", "/Users/<USER>/Puzzles/Builder/ColorZero/src/contexts/ColorContext.tsx": "12", "/Users/<USER>/Puzzles/Builder/ColorZero/src/solution.ts": "13", "/Users/<USER>/Puzzles/Builder/ColorZero/src/Cell.tsx": "14"}, {"size": 554, "mtime": 1741061891307, "results": "15", "hashOfConfig": "16"}, {"size": 425, "mtime": 1741061891308, "results": "17", "hashOfConfig": "16"}, {"size": 457, "mtime": 1741073716236, "results": "18", "hashOfConfig": "16"}, {"size": 16059, "mtime": 1741392425584, "results": "19", "hashOfConfig": "16"}, {"size": 2274, "mtime": 1741142424068, "results": "20", "hashOfConfig": "16"}, {"size": 727, "mtime": 1741067477855, "results": "21", "hashOfConfig": "16"}, {"size": 6929, "mtime": 1741503742449, "results": "22", "hashOfConfig": "16"}, {"size": 554, "mtime": 1754794523207, "results": "23", "hashOfConfig": "24"}, {"size": 425, "mtime": 1754794523209, "results": "25", "hashOfConfig": "24"}, {"size": 457, "mtime": 1754794523202, "results": "26", "hashOfConfig": "24"}, {"size": 15758, "mtime": 1754797925449, "results": "27", "hashOfConfig": "24"}, {"size": 727, "mtime": 1754794523206, "results": "28", "hashOfConfig": "24"}, {"size": 6929, "mtime": 1754794523211, "results": "29", "hashOfConfig": "24"}, {"size": 2530, "mtime": 1754797712408, "results": "30", "hashOfConfig": "24"}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1by9moz", {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "t24dif", {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/LightThem/Builder/builder/src/index.tsx", [], [], "/Users/<USER>/LightThem/Builder/builder/src/reportWebVitals.ts", [], [], "/Users/<USER>/LightThem/Builder/builder/src/App.tsx", [], [], "/Users/<USER>/LightThem/Builder/builder/src/Board.tsx", ["73"], [], "/Users/<USER>/LightThem/Builder/builder/src/Cell.tsx", [], [], "/Users/<USER>/LightThem/Builder/builder/src/contexts/ColorContext.tsx", [], [], "/Users/<USER>/LightThem/Builder/builder/src/solution.ts", ["74", "75", "76"], [], "/Users/<USER>/Puzzles/Builder/ColorZero/src/index.tsx", [], [], "/Users/<USER>/Puzzles/Builder/ColorZero/src/reportWebVitals.ts", [], [], "/Users/<USER>/Puzzles/Builder/ColorZero/src/App.tsx", [], [], "/Users/<USER>/Puzzles/Builder/ColorZero/src/Board.tsx", [], [], "/Users/<USER>/Puzzles/Builder/ColorZero/src/contexts/ColorContext.tsx", [], [], "/Users/<USER>/Puzzles/Builder/ColorZero/src/solution.ts", ["77", "78", "79"], [], "/Users/<USER>/Puzzles/Builder/ColorZero/src/Cell.tsx", [], [], {"ruleId": "80", "severity": 1, "message": "81", "line": 502, "column": 29, "nodeType": "82", "messageId": "83", "endLine": 502, "endColumn": 39}, {"ruleId": "84", "severity": 1, "message": "85", "line": 1, "column": 1, "nodeType": "86", "messageId": "87", "fix": "88"}, {"ruleId": "89", "severity": 1, "message": "90", "line": 223, "column": 29, "nodeType": "91", "messageId": "92", "endLine": 223, "endColumn": 58}, {"ruleId": "89", "severity": 1, "message": "90", "line": 228, "column": 36, "nodeType": "91", "messageId": "92", "endLine": 228, "endColumn": 65}, {"ruleId": "84", "severity": 1, "message": "85", "line": 1, "column": 1, "nodeType": "86", "messageId": "87", "fix": "93"}, {"ruleId": "89", "severity": 1, "message": "90", "line": 223, "column": 29, "nodeType": "91", "messageId": "92", "endLine": 223, "endColumn": 58}, {"ruleId": "89", "severity": 1, "message": "90", "line": 228, "column": 36, "nodeType": "91", "messageId": "92", "endLine": 228, "endColumn": 65}, "@typescript-eslint/no-unused-vars", "'markedCell' is assigned a value but never used.", "Identifier", "unusedVar", "unicode-bom", "Unexpected Unicode BOM (Byte Order Mark).", "Program", "unexpected", {"range": "94", "text": "95"}, "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'i', 'j'.", "ArrowFunctionExpression", "unsafeRefs", {"range": "96", "text": "95"}, [-1, 0], "", [-1, 0]]