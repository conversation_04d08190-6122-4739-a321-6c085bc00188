{"ast": null, "code": "var copyObject = require('./_copyObject'),\n  keys = require('./keys');\n\n/**\n * The base implementation of `_.assign` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */\nfunction baseAssign(object, source) {\n  return object && copyObject(source, keys(source), object);\n}\nmodule.exports = baseAssign;", "map": {"version": 3, "names": ["copyObject", "require", "keys", "baseAssign", "object", "source", "module", "exports"], "sources": ["/Users/<USER>/Puzzles/Builder/ColorZero/node_modules/lodash/_baseAssign.js"], "sourcesContent": ["var copyObject = require('./_copyObject'),\n    keys = require('./keys');\n\n/**\n * The base implementation of `_.assign` without support for multiple sources\n * or `customizer` functions.\n *\n * @private\n * @param {Object} object The destination object.\n * @param {Object} source The source object.\n * @returns {Object} Returns `object`.\n */\nfunction baseAssign(object, source) {\n  return object && copyObject(source, keys(source), object);\n}\n\nmodule.exports = baseAssign;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,eAAe,CAAC;EACrCC,IAAI,GAAGD,OAAO,CAAC,QAAQ,CAAC;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAClC,OAAOD,MAAM,IAAIJ,UAAU,CAACK,MAAM,EAAEH,IAAI,CAACG,MAAM,CAAC,EAAED,MAAM,CAAC;AAC3D;AAEAE,MAAM,CAACC,OAAO,GAAGJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}