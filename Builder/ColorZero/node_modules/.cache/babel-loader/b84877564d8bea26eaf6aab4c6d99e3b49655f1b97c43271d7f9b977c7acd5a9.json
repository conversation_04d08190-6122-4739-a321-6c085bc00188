{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Puzzles/Builder/ColorZero/src/Cell.tsx\",\n  _s = $RefreshSig$();\nimport React, { forwardRef, useImperativeHandle, useState } from \"react\";\nimport { useColor } from \"./contexts/ColorContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Width = 100;\nconst Cell = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  i,\n  j,\n  size\n}, ref) => {\n  _s();\n  const {\n    activeColor\n  } = useColor();\n  const [color, setColor] = useState(\"white\");\n  const [power, setPower] = useState(\"\");\n  const [marked, setMarked] = useState(false);\n  var index = i * size + j + 1;\n  useImperativeHandle(ref, () => ({\n    getState: () => ({\n      color,\n      power,\n      marked\n    })\n  }));\n  const handleClick = () => {\n    if (activeColor === \"+\" || activeColor === \"-\") {\n      if (color === \"white\" || color === \"gray\") {\n        alert(\"Power symbols are not allowed on white or gray cells\");\n        return;\n      }\n      setPower(power === activeColor ? \"\" : activeColor); // Toggle power\n    } else if (activeColor === \".\") {\n      if (color === \"white\" || color === \"gray\") {\n        alert(\"Marked dots are not allowed on white or gray cells\");\n        return;\n      }\n      setMarked(!marked); // Toggle marked state\n    } else {\n      if (activeColor === \"white\" || activeColor === \"gray\") {\n        setPower(\"\");\n        setMarked(false);\n      }\n      setColor(color === activeColor ? \"white\" : activeColor); // Toggle color\n    }\n  };\n  const displayText = marked ? /*#__PURE__*/_jsxDEV(\"span\", {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        fontSize: \"24px\"\n      },\n      children: index\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 57,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        fontSize: \"48px\"\n      },\n      children: \"\\u2022\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        fontSize: \"24px\"\n      },\n      children: power\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this) : `${index}${power}`;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: Width,\n      height: Width,\n      border: \"1px solid black\",\n      position: \"absolute\",\n      left: j * Width,\n      top: i * Width,\n      backgroundColor: color,\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      color: \"black\",\n      fontSize: marked ? \"36px\" : \"24px\",\n      // Larger font size when marked\n      cursor: \"pointer\"\n    },\n    onClick: handleClick,\n    children: displayText\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n}, \"6xqm0DMIv5Hc8pDRy+jIQozhvck=\", false, function () {\n  return [useColor];\n})), \"6xqm0DMIv5Hc8pDRy+jIQozhvck=\", false, function () {\n  return [useColor];\n});\n_c2 = Cell;\nexport default Cell;\nvar _c, _c2;\n$RefreshReg$(_c, \"Cell$forwardRef\");\n$RefreshReg$(_c2, \"Cell\");", "map": {"version": 3, "names": ["React", "forwardRef", "useImperativeHandle", "useState", "useColor", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "Cell", "_s", "_c", "i", "j", "size", "ref", "activeColor", "color", "setColor", "power", "<PERSON><PERSON><PERSON><PERSON>", "marked", "setMarked", "index", "getState", "handleClick", "alert", "displayText", "children", "style", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "border", "position", "left", "top", "backgroundColor", "display", "alignItems", "justifyContent", "cursor", "onClick", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Puzzles/Builder/ColorZero/src/Cell.tsx"], "sourcesContent": ["import React, { forwardRef, useImperativeHandle, useState } from \"react\";\nimport { useColor } from \"./contexts/ColorContext\";\n\nconst Width = 100;\n\ninterface CellProps {\n  i: number;\n  j: number;\n  size: number;\n}\n\ninterface CellState {\n  color: string;\n  power: string;\n  marked: boolean;\n}\n\nconst Cell = forwardRef<{ getState: () => CellState }, CellProps>(({ i, j, size }, ref) => {\n  const { activeColor } = useColor();\n  const [color, setColor] = useState(\"white\");\n  const [power, setPower] = useState(\"\");\n  const [marked, setMarked] = useState(false);\n  var index = i * size + j + 1;\n  \n  useImperativeHandle(ref, () => ({\n    getState: () => ({\n      color,\n      power,\n      marked\n    })\n  }));\n\n  const handleClick = () => {\n    if (activeColor === \"+\" || activeColor === \"-\") {\n      if (color === \"white\" || color === \"gray\") {\n        alert(\"Power symbols are not allowed on white or gray cells\");\n        return;\n      }\n      setPower(power === activeColor ? \"\" : activeColor);  // Toggle power\n    } else if (activeColor === \".\") {\n      if (color === \"white\" || color === \"gray\") {\n        alert(\"Marked dots are not allowed on white or gray cells\");\n        return;\n      }\n      setMarked(!marked);  // Toggle marked state\n    } else {\n      if (activeColor === \"white\" || activeColor === \"gray\") {\n        setPower(\"\");\n        setMarked(false);\n      }\n      setColor(color === activeColor ? \"white\" : activeColor);  // Toggle color\n    }\n  };\n\n  const displayText = marked ? (\n    <span>\n      <span style={{ fontSize: \"24px\" }}>{index}</span>\n      <span style={{ fontSize: \"48px\" }}>&bull;</span>\n      <span style={{ fontSize: \"24px\" }}>{power}</span>\n    </span>\n  ) : (\n    `${index}${power}`\n  );\n\n  return (\n    <div\n      style={{\n        width: Width,\n        height: Width,\n        border: \"1px solid black\",\n        position: \"absolute\",\n        left: j * Width,\n        top: i * Width,\n        backgroundColor: color,\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        color: \"black\",\n        fontSize: marked ? \"36px\" : \"24px\", // Larger font size when marked\n        cursor: \"pointer\"\n      }}\n      onClick={handleClick}\n    >\n      {displayText}\n    </div>\n  );\n});\n\nexport default Cell;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,mBAAmB,EAAEC,QAAQ,QAAQ,OAAO;AACxE,SAASC,QAAQ,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,KAAK,GAAG,GAAG;AAcjB,MAAMC,IAAI,gBAAAC,EAAA,cAAGR,UAAU,CAAAS,EAAA,GAAAD,EAAA,CAA2C,CAAC;EAAEE,CAAC;EAAEC,CAAC;EAAEC;AAAK,CAAC,EAAEC,GAAG,KAAK;EAAAL,EAAA;EACzF,MAAM;IAAEM;EAAY,CAAC,GAAGX,QAAQ,CAAC,CAAC;EAClC,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,OAAO,CAAC;EAC3C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,MAAM,EAAEC,SAAS,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC3C,IAAImB,KAAK,GAAGX,CAAC,GAAGE,IAAI,GAAGD,CAAC,GAAG,CAAC;EAE5BV,mBAAmB,CAACY,GAAG,EAAE,OAAO;IAC9BS,QAAQ,EAAEA,CAAA,MAAO;MACfP,KAAK;MACLE,KAAK;MACLE;IACF,CAAC;EACH,CAAC,CAAC,CAAC;EAEH,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIT,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,EAAE;MAC9C,IAAIC,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,MAAM,EAAE;QACzCS,KAAK,CAAC,sDAAsD,CAAC;QAC7D;MACF;MACAN,QAAQ,CAACD,KAAK,KAAKH,WAAW,GAAG,EAAE,GAAGA,WAAW,CAAC,CAAC,CAAE;IACvD,CAAC,MAAM,IAAIA,WAAW,KAAK,GAAG,EAAE;MAC9B,IAAIC,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,MAAM,EAAE;QACzCS,KAAK,CAAC,oDAAoD,CAAC;QAC3D;MACF;MACAJ,SAAS,CAAC,CAACD,MAAM,CAAC,CAAC,CAAE;IACvB,CAAC,MAAM;MACL,IAAIL,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,MAAM,EAAE;QACrDI,QAAQ,CAAC,EAAE,CAAC;QACZE,SAAS,CAAC,KAAK,CAAC;MAClB;MACAJ,QAAQ,CAACD,KAAK,KAAKD,WAAW,GAAG,OAAO,GAAGA,WAAW,CAAC,CAAC,CAAE;IAC5D;EACF,CAAC;EAED,MAAMW,WAAW,GAAGN,MAAM,gBACxBd,OAAA;IAAAqB,QAAA,gBACErB,OAAA;MAAMsB,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAF,QAAA,EAAEL;IAAK;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACjD3B,OAAA;MAAMsB,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAF,QAAA,EAAC;IAAM;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAChD3B,OAAA;MAAMsB,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAF,QAAA,EAAET;IAAK;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7C,CAAC,GAEP,GAAGX,KAAK,GAAGJ,KAAK,EACjB;EAED,oBACEZ,OAAA;IACEsB,KAAK,EAAE;MACLM,KAAK,EAAE3B,KAAK;MACZ4B,MAAM,EAAE5B,KAAK;MACb6B,MAAM,EAAE,iBAAiB;MACzBC,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE1B,CAAC,GAAGL,KAAK;MACfgC,GAAG,EAAE5B,CAAC,GAAGJ,KAAK;MACdiC,eAAe,EAAExB,KAAK;MACtByB,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxB3B,KAAK,EAAE,OAAO;MACda,QAAQ,EAAET,MAAM,GAAG,MAAM,GAAG,MAAM;MAAE;MACpCwB,MAAM,EAAE;IACV,CAAE;IACFC,OAAO,EAAErB,WAAY;IAAAG,QAAA,EAEpBD;EAAW;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;EAAA,QApEyB7B,QAAQ;AAAA,EAoEjC,CAAC;EAAA,QApEwBA,QAAQ;AAAA,EAoEhC;AAAC0C,GAAA,GArEGtC,IAAI;AAuEV,eAAeA,IAAI;AAAC,IAAAE,EAAA,EAAAoC,GAAA;AAAAC,YAAA,CAAArC,EAAA;AAAAqC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}