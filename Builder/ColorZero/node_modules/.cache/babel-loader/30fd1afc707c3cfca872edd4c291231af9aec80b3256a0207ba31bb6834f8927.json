{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Puzzles/Builder/ColorZero/src/Cell.tsx\",\n  _s = $RefreshSig$();\nimport React, { forwardRef, useImperativeHandle, useState } from \"react\";\nimport { useColor } from \"./contexts/ColorContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Width = 100;\nconst Cell = /*#__PURE__*/_s(/*#__PURE__*/forwardRef(_c = _s(({\n  i,\n  j,\n  size\n}, ref) => {\n  _s();\n  const {\n    activeColor\n  } = useColor();\n  const [color, setColor] = useState(\"white\");\n  const [power, setPower] = useState(\"\");\n  const [markNumber, setMarkNumber] = useState(0);\n  var index = i * size + j + 1;\n  useImperativeHandle(ref, () => ({\n    getState: () => ({\n      color,\n      power,\n      markNumber\n    })\n  }));\n  const handleClick = () => {\n    if (activeColor === \"A\" || activeColor === \"B\" || activeColor === \"C\" || activeColor === \"D\") {\n      if (color === \"white\" || color === \"gray\") {\n        alert(\"Power symbols are not allowed on white or gray cells\");\n        return;\n      }\n      setPower(power === activeColor ? \"\" : activeColor); // Toggle power\n    } else if (activeColor === \"1\" || activeColor === \"2\" || activeColor === \"3\" || activeColor === \"4\") {\n      if (color === \"white\" || color === \"gray\") {\n        alert(\"Number marks are not allowed on white or gray cells\");\n        return;\n      }\n      const num = parseInt(activeColor);\n      setMarkNumber(markNumber === num ? 0 : num); // Toggle mark number\n    } else {\n      if (activeColor === \"white\" || activeColor === \"gray\") {\n        setPower(\"\");\n        setMarkNumber(0);\n      }\n      setColor(color === activeColor ? \"white\" : activeColor); // Toggle color\n    }\n  };\n  const displayText = markNumber > 0 ? /*#__PURE__*/_jsxDEV(\"span\", {\n    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        fontSize: \"24px\"\n      },\n      children: index\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 58,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        fontSize: \"48px\",\n        color: \"white\"\n      },\n      children: markNumber\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      style: {\n        fontSize: \"24px\"\n      },\n      children: power\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 5\n  }, this) : `${index}${power}`;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      width: Width,\n      height: Width,\n      border: \"1px solid black\",\n      position: \"absolute\",\n      left: j * Width,\n      top: i * Width,\n      backgroundColor: color,\n      display: \"flex\",\n      alignItems: \"center\",\n      justifyContent: \"center\",\n      color: \"black\",\n      fontSize: markNumber > 0 ? \"36px\" : \"24px\",\n      // Larger font size when marked\n      cursor: \"pointer\"\n    },\n    onClick: handleClick,\n    children: displayText\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n}, \"bimiWPgCJCobjtfBdK+bOfsBQxU=\", false, function () {\n  return [useColor];\n})), \"bimiWPgCJCobjtfBdK+bOfsBQxU=\", false, function () {\n  return [useColor];\n});\n_c2 = Cell;\nexport default Cell;\nvar _c, _c2;\n$RefreshReg$(_c, \"Cell$forwardRef\");\n$RefreshReg$(_c2, \"Cell\");", "map": {"version": 3, "names": ["React", "forwardRef", "useImperativeHandle", "useState", "useColor", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON>", "Cell", "_s", "_c", "i", "j", "size", "ref", "activeColor", "color", "setColor", "power", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setMarkNumber", "index", "getState", "handleClick", "alert", "num", "parseInt", "displayText", "children", "style", "fontSize", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "width", "height", "border", "position", "left", "top", "backgroundColor", "display", "alignItems", "justifyContent", "cursor", "onClick", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/Puzzles/Builder/ColorZero/src/Cell.tsx"], "sourcesContent": ["import React, { forwardRef, useImperativeHandle, useState } from \"react\";\nimport { useColor } from \"./contexts/ColorContext\";\n\nconst Width = 100;\n\ninterface CellProps {\n  i: number;\n  j: number;\n  size: number;\n}\n\ninterface CellState {\n  color: string;\n  power: string;\n  markNumber: number; // 0 = not marked, 1-4 = marked with number\n}\n\nconst Cell = forwardRef<{ getState: () => CellState }, CellProps>(({ i, j, size }, ref) => {\n  const { activeColor } = useColor();\n  const [color, setColor] = useState(\"white\");\n  const [power, setPower] = useState(\"\");\n  const [markNumber, setMarkNumber] = useState(0);\n  var index = i * size + j + 1;\n\n  useImperativeHandle(ref, () => ({\n    getState: () => ({\n      color,\n      power,\n      markNumber\n    })\n  }));\n\n  const handleClick = () => {\n    if (activeColor === \"A\" || activeColor === \"B\" || activeColor === \"C\" || activeColor === \"D\") {\n      if (color === \"white\" || color === \"gray\") {\n        alert(\"Power symbols are not allowed on white or gray cells\");\n        return;\n      }\n      setPower(power === activeColor ? \"\" : activeColor);  // Toggle power\n    } else if (activeColor === \"1\" || activeColor === \"2\" || activeColor === \"3\" || activeColor === \"4\") {\n      if (color === \"white\" || color === \"gray\") {\n        alert(\"Number marks are not allowed on white or gray cells\");\n        return;\n      }\n      const num = parseInt(activeColor);\n      setMarkNumber(markNumber === num ? 0 : num);  // Toggle mark number\n    } else {\n      if (activeColor === \"white\" || activeColor === \"gray\") {\n        setPower(\"\");\n        setMarkNumber(0);\n      }\n      setColor(color === activeColor ? \"white\" : activeColor);  // Toggle color\n    }\n  };\n\n  const displayText = markNumber > 0 ? (\n    <span>\n      <span style={{ fontSize: \"24px\" }}>{index}</span>\n      <span style={{ fontSize: \"48px\", color: \"white\" }}>{markNumber}</span>\n      <span style={{ fontSize: \"24px\" }}>{power}</span>\n    </span>\n  ) : (\n    `${index}${power}`\n  );\n\n  return (\n    <div\n      style={{\n        width: Width,\n        height: Width,\n        border: \"1px solid black\",\n        position: \"absolute\",\n        left: j * Width,\n        top: i * Width,\n        backgroundColor: color,\n        display: \"flex\",\n        alignItems: \"center\",\n        justifyContent: \"center\",\n        color: \"black\",\n        fontSize: markNumber > 0 ? \"36px\" : \"24px\", // Larger font size when marked\n        cursor: \"pointer\"\n      }}\n      onClick={handleClick}\n    >\n      {displayText}\n    </div>\n  );\n});\n\nexport default Cell;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,UAAU,EAAEC,mBAAmB,EAAEC,QAAQ,QAAQ,OAAO;AACxE,SAASC,QAAQ,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,KAAK,GAAG,GAAG;AAcjB,MAAMC,IAAI,gBAAAC,EAAA,cAAGR,UAAU,CAAAS,EAAA,GAAAD,EAAA,CAA2C,CAAC;EAAEE,CAAC;EAAEC,CAAC;EAAEC;AAAK,CAAC,EAAEC,GAAG,KAAK;EAAAL,EAAA;EACzF,MAAM;IAAEM;EAAY,CAAC,GAAGX,QAAQ,CAAC,CAAC;EAClC,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,OAAO,CAAC;EAC3C,MAAM,CAACe,KAAK,EAAEC,QAAQ,CAAC,GAAGhB,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiB,UAAU,EAAEC,aAAa,CAAC,GAAGlB,QAAQ,CAAC,CAAC,CAAC;EAC/C,IAAImB,KAAK,GAAGX,CAAC,GAAGE,IAAI,GAAGD,CAAC,GAAG,CAAC;EAE5BV,mBAAmB,CAACY,GAAG,EAAE,OAAO;IAC9BS,QAAQ,EAAEA,CAAA,MAAO;MACfP,KAAK;MACLE,KAAK;MACLE;IACF,CAAC;EACH,CAAC,CAAC,CAAC;EAEH,MAAMI,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAIT,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,EAAE;MAC5F,IAAIC,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,MAAM,EAAE;QACzCS,KAAK,CAAC,sDAAsD,CAAC;QAC7D;MACF;MACAN,QAAQ,CAACD,KAAK,KAAKH,WAAW,GAAG,EAAE,GAAGA,WAAW,CAAC,CAAC,CAAE;IACvD,CAAC,MAAM,IAAIA,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,IAAIA,WAAW,KAAK,GAAG,EAAE;MACnG,IAAIC,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,MAAM,EAAE;QACzCS,KAAK,CAAC,qDAAqD,CAAC;QAC5D;MACF;MACA,MAAMC,GAAG,GAAGC,QAAQ,CAACZ,WAAW,CAAC;MACjCM,aAAa,CAACD,UAAU,KAAKM,GAAG,GAAG,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAE;IAChD,CAAC,MAAM;MACL,IAAIX,WAAW,KAAK,OAAO,IAAIA,WAAW,KAAK,MAAM,EAAE;QACrDI,QAAQ,CAAC,EAAE,CAAC;QACZE,aAAa,CAAC,CAAC,CAAC;MAClB;MACAJ,QAAQ,CAACD,KAAK,KAAKD,WAAW,GAAG,OAAO,GAAGA,WAAW,CAAC,CAAC,CAAE;IAC5D;EACF,CAAC;EAED,MAAMa,WAAW,GAAGR,UAAU,GAAG,CAAC,gBAChCd,OAAA;IAAAuB,QAAA,gBACEvB,OAAA;MAAMwB,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAF,QAAA,EAAEP;IAAK;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACjD7B,OAAA;MAAMwB,KAAK,EAAE;QAAEC,QAAQ,EAAE,MAAM;QAAEf,KAAK,EAAE;MAAQ,CAAE;MAAAa,QAAA,EAAET;IAAU;MAAAY,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eACtE7B,OAAA;MAAMwB,KAAK,EAAE;QAAEC,QAAQ,EAAE;MAAO,CAAE;MAAAF,QAAA,EAAEX;IAAK;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7C,CAAC,GAEP,GAAGb,KAAK,GAAGJ,KAAK,EACjB;EAED,oBACEZ,OAAA;IACEwB,KAAK,EAAE;MACLM,KAAK,EAAE7B,KAAK;MACZ8B,MAAM,EAAE9B,KAAK;MACb+B,MAAM,EAAE,iBAAiB;MACzBC,QAAQ,EAAE,UAAU;MACpBC,IAAI,EAAE5B,CAAC,GAAGL,KAAK;MACfkC,GAAG,EAAE9B,CAAC,GAAGJ,KAAK;MACdmC,eAAe,EAAE1B,KAAK;MACtB2B,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxB7B,KAAK,EAAE,OAAO;MACde,QAAQ,EAAEX,UAAU,GAAG,CAAC,GAAG,MAAM,GAAG,MAAM;MAAE;MAC5C0B,MAAM,EAAE;IACV,CAAE;IACFC,OAAO,EAAEvB,WAAY;IAAAK,QAAA,EAEpBD;EAAW;IAAAI,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEV,CAAC;EAAA,QArEyB/B,QAAQ;AAAA,EAqEjC,CAAC;EAAA,QArEwBA,QAAQ;AAAA,EAqEhC;AAAC4C,GAAA,GAtEGxC,IAAI;AAwEV,eAAeA,IAAI;AAAC,IAAAE,EAAA,EAAAsC,GAAA;AAAAC,YAAA,CAAAvC,EAAA;AAAAuC,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}