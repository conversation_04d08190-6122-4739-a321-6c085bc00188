{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Puzzles/Builder/ColorZero/src/contexts/ColorContext.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ColorContext = /*#__PURE__*/createContext(undefined);\nexport const ColorProvider = ({\n  children\n}) => {\n  _s();\n  const [activeColor, setActiveColor] = useState(\"red\");\n  return /*#__PURE__*/_jsxDEV(ColorContext.Provider, {\n    value: {\n      activeColor,\n      setActiveColor\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_s(ColorProvider, \"/fqIQUoYneglDE/AWlsCYoOnUr8=\");\n_c = ColorProvider;\nexport const useColor = () => {\n  _s2();\n  const context = useContext(ColorContext);\n  if (context === undefined) {\n    throw new Error('useColor must be used within a ColorProvider');\n  }\n  return context;\n};\n_s2(useColor, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"ColorProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "jsxDEV", "_jsxDEV", "ColorContext", "undefined", "ColorProvider", "children", "_s", "activeColor", "setActiveColor", "Provider", "value", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "useColor", "_s2", "context", "Error", "$RefreshReg$"], "sources": ["/Users/<USER>/Puzzles/Builder/ColorZero/src/contexts/ColorContext.tsx"], "sourcesContent": ["import React, { createContext, useContext, useState } from 'react';\n\ninterface ColorContextType {\n  activeColor: string;\n  setActiveColor: (color: string) => void;\n}\n\nconst ColorContext = createContext<ColorContextType | undefined>(undefined);\n\nexport const ColorProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [activeColor, setActiveColor] = useState(\"red\");\n\n  return (\n    <ColorContext.Provider value={{ activeColor, setActiveColor }}>\n      {children}\n    </ColorContext.Provider>\n  );\n};\n\nexport const useColor = () => {\n  const context = useContext(ColorContext);\n  if (context === undefined) {\n    throw new Error('useColor must be used within a ColorProvider');\n  }\n  return context;\n};"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOnE,MAAMC,YAAY,gBAAGL,aAAa,CAA+BM,SAAS,CAAC;AAE3E,OAAO,MAAMC,aAAsD,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;EAErD,oBACEE,OAAA,CAACC,YAAY,CAACO,QAAQ;IAACC,KAAK,EAAE;MAAEH,WAAW;MAAEC;IAAe,CAAE;IAAAH,QAAA,EAC3DA;EAAQ;IAAAM,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACY,CAAC;AAE5B,CAAC;AAACR,EAAA,CARWF,aAAsD;AAAAW,EAAA,GAAtDX,aAAsD;AAUnE,OAAO,MAAMY,QAAQ,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC5B,MAAMC,OAAO,GAAGpB,UAAU,CAACI,YAAY,CAAC;EACxC,IAAIgB,OAAO,KAAKf,SAAS,EAAE;IACzB,MAAM,IAAIgB,KAAK,CAAC,8CAA8C,CAAC;EACjE;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,GAAA,CANWD,QAAQ;AAAA,IAAAD,EAAA;AAAAK,YAAA,CAAAL,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}