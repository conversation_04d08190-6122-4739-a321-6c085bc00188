{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Puzzles/Builder/ColorZero/src/Board.tsx\",\n  _s = $RefreshSig$();\nimport React, { useRef } from \"react\";\nimport Cell from \"./Cell\";\nimport { useColor } from \"./contexts/ColorContext\";\nimport { getAllSolutions } from \"./solution\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst COLORS = [\"white\", \"#ff0000\", \"#00ff00\", \"#0000ff\", \"#ffff00\", \"#00ffff\", \"#ff00ff\", \"gray\"];\nconst colorButtonStyle = (color, isActive) => ({\n  backgroundColor: color,\n  width: \"50px\",\n  height: \"50px\",\n  border: isActive ? \"3px solid black\" : \"1px solid gray\"\n});\nconst powerButtonStyle = (power, isActive) => ({\n  backgroundColor: \"white\",\n  width: \"50px\",\n  height: \"50px\",\n  border: isActive ? \"3px solid black\" : \"1px solid gray\",\n  fontSize: \"24px\",\n  display: \"flex\",\n  alignItems: \"center\",\n  justifyContent: \"center\"\n});\nconst Board = ({\n  size: initialSize = 4\n}) => {\n  _s();\n  const {\n    activeColor,\n    setActiveColor\n  } = useColor();\n  const cellRefs = useRef({});\n  const [output, setOutput] = React.useState(\"\");\n  const [name, setName] = React.useState(\"1\");\n  const [size, setSize] = React.useState(initialSize);\n  const [solutions, setSolutions] = React.useState([]);\n  const [executionTime, setExecutionTime] = React.useState(null);\n  const findConnectedCells = color => {\n    // Create a map of cell positions with this color\n    const colorCells = {};\n    const visited = {};\n\n    // Fill the map\n    for (let i = 0; i < size; i++) {\n      for (let j = 0; j < size; j++) {\n        var _cellRefs$current$key;\n        const key = `${i}-${j}`;\n        const cellState = (_cellRefs$current$key = cellRefs.current[key]) === null || _cellRefs$current$key === void 0 ? void 0 : _cellRefs$current$key.getState();\n        if (cellState && cellState.color === color) {\n          colorCells[key] = true;\n        }\n      }\n    }\n    const getConnectedGroup = (i, j) => {\n      const group = [];\n      const markedCells = [];\n      const stack = [[i, j]];\n      while (stack.length > 0) {\n        const [currI, currJ] = stack.pop();\n        const key = `${currI}-${currJ}`;\n        if (visited[key]) continue;\n        visited[key] = true;\n        if (colorCells[key]) {\n          var _cellRefs$current$key2;\n          const index = currI * size + currJ + 1;\n          const cellState = (_cellRefs$current$key2 = cellRefs.current[key]) === null || _cellRefs$current$key2 === void 0 ? void 0 : _cellRefs$current$key2.getState();\n\n          // If cell is marked with \".\", add to markedCells, otherwise add to regular group\n          if (cellState && cellState.marked) {\n            markedCells.push(index);\n          } else {\n            group.push(index);\n          }\n\n          // Check adjacent cells (up, down, left, right)\n          const adjacent = [[currI - 1, currJ], [currI + 1, currJ], [currI, currJ - 1], [currI, currJ + 1]];\n          for (const [nextI, nextJ] of adjacent) {\n            if (nextI >= 0 && nextI < size && nextJ >= 0 && nextJ < size) {\n              const nextKey = `${nextI}-${nextJ}`;\n              if (colorCells[nextKey] && !visited[nextKey]) {\n                stack.push([nextI, nextJ]);\n              }\n            }\n          }\n        }\n      }\n\n      // Combine arrays with marked cells first\n      return [...markedCells, ...group];\n    };\n    const groups = [];\n\n    // Find all connected components\n    for (let i = 0; i < size; i++) {\n      for (let j = 0; j < size; j++) {\n        const key = `${i}-${j}`;\n        if (colorCells[key] && !visited[key]) {\n          const group = getConnectedGroup(i, j);\n          if (group.length > 0) {\n            groups.push(group);\n          }\n        }\n      }\n    }\n    return groups;\n  };\n  const handleExport = () => {\n    const bannedCells = [];\n    const aPowerCells = [];\n    const bPowerCells = [];\n    const cPowerCells = [];\n    const dPowerCells = [];\n    const aliveGroups = [];\n\n    // Get banned and power cells\n    for (let i = 0; i < size; i++) {\n      for (let j = 0; j < size; j++) {\n        var _cellRefs$current$key3;\n        const key = `${i}-${j}`;\n        const cellState = (_cellRefs$current$key3 = cellRefs.current[key]) === null || _cellRefs$current$key3 === void 0 ? void 0 : _cellRefs$current$key3.getState();\n        if (cellState) {\n          const index = i * size + j + 1;\n          if (cellState.color === \"gray\") {\n            bannedCells.push(index);\n          }\n          if (cellState.power === \"A\") {\n            aPowerCells.push(index);\n          }\n          if (cellState.power === \"B\") {\n            bPowerCells.push(index);\n          }\n          if (cellState.power === \"C\") {\n            cPowerCells.push(index);\n          }\n          if (cellState.power === \"D\") {\n            dPowerCells.push(index);\n          }\n        }\n      }\n    }\n\n    // Get connected components for each color except white and gray\n    let isValid = true;\n    for (const color of COLORS) {\n      if (color !== \"white\" && color !== \"gray\") {\n        const groups = findConnectedCells(color);\n        // Check if each group has exactly one marked cell\n        for (const group of groups) {\n          const markedCells = group.filter(index => {\n            var _cellRefs$current$key4;\n            const i = Math.floor((index - 1) / size);\n            const j = (index - 1) % size;\n            const key = `${i}-${j}`;\n            const cellState = (_cellRefs$current$key4 = cellRefs.current[key]) === null || _cellRefs$current$key4 === void 0 ? void 0 : _cellRefs$current$key4.getState();\n            return cellState === null || cellState === void 0 ? void 0 : cellState.marked;\n          });\n          if (markedCells.length !== 1) {\n            alert(`Invalid group: each connected group must have exactly one marked cell`);\n            isValid = false;\n            break;\n          }\n        }\n        if (!isValid) break;\n        aliveGroups.push(...groups);\n      }\n    }\n    if (!isValid) {\n      return null;\n    }\n\n    // Sort aliveGroups by length in descending order\n    aliveGroups.sort((a, b) => b.length - a.length);\n\n    // Create output object starting with \"n\" field\n    const output = {\n      n: name,\n      a: aliveGroups\n    };\n\n    // Only add non-empty arrays to output\n    if (bannedCells.length > 0) {\n      output[\"b\"] = bannedCells;\n    }\n    if (aPowerCells.length > 0) {\n      output[\"A\"] = aPowerCells;\n    }\n    if (bPowerCells.length > 0) {\n      output[\"B\"] = bPowerCells;\n    }\n    if (cPowerCells.length > 0) {\n      output[\"C\"] = cPowerCells;\n    }\n    if (dPowerCells.length > 0) {\n      output[\"D\"] = dPowerCells;\n    }\n    setOutput(JSON.stringify(output));\n    return output;\n  };\n  const handleSolve = () => {\n    const gameDef = handleExport();\n    if (gameDef == null) {\n      return;\n    }\n    const startTime = performance.now();\n    const result = getAllSolutions(size, gameDef);\n    const endTime = performance.now();\n    const executionTime = endTime - startTime;\n    setExecutionTime(executionTime);\n    setSolutions(result || []);\n  };\n  const cells = [];\n  for (let i = 0; i < size; i++) {\n    for (let j = 0; j < size; j++) {\n      cells.push(/*#__PURE__*/_jsxDEV(Cell, {\n        i: i,\n        j: j,\n        size: size,\n        ref: ref => {\n          if (ref) {\n            cellRefs.current[`${i}-${j}`] = ref;\n          }\n        }\n      }, `${i}-${j}`, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this));\n    }\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      display: \"flex\",\n      gap: \"20px\",\n      padding: \"20px\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        flex: \"1\",\n        backgroundColor: \"white\",\n        padding: \"20px\",\n        borderRadius: \"8px\",\n        boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: \"relative\"\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: \"20px\",\n            display: \"flex\",\n            justifyContent: \"center\"\n          },\n          children: /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"number\",\n            value: size,\n            onChange: e => setSize(parseInt(e.target.value)),\n            style: {\n              width: \"50px\",\n              height: \"50px\",\n              textAlign: \"center\",\n              fontSize: \"24px\"\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 286,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 279,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: \"relative\",\n            width: size * 100,\n            height: size * 100\n          },\n          children: cells\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 299,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"board-footer\",\n          style: {\n            marginTop: \"20px\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            gap: \"10px\",\n            alignItems: \"flex-start\"\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              gap: \"10px\"\n            },\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: name,\n              onChange: e => setName(e.target.value),\n              style: {\n                width: \"50px\",\n                height: \"50px\",\n                textAlign: \"center\",\n                fontSize: \"24px\"\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              gap: \"10px\"\n            },\n            children: COLORS.map(color => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveColor(color),\n              style: colorButtonStyle(color, activeColor === color)\n            }, color, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 335,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              gap: \"10px\"\n            },\n            children: [[\"A\", \"B\", \"C\", \"D\"].map(power => /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveColor(power),\n              style: powerButtonStyle(power, activeColor === power),\n              children: power\n            }, power, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 17\n            }, this)), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => setActiveColor(\".\"),\n              style: {\n                ...powerButtonStyle(\".\", activeColor === \".\"),\n                fontSize: \"36px\",\n                lineHeight: \"36px\"\n              },\n              children: \"\\u2022\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 356,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 346,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: \"flex\",\n              gap: \"10px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleExport,\n              style: {\n                ...powerButtonStyle(\"export\", false),\n                width: \"auto\",\n                padding: \"0 20px\"\n              },\n              children: \"Export\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 370,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: handleSolve,\n              style: {\n                ...powerButtonStyle(\"solve\", false),\n                width: \"auto\",\n                padding: \"0 20px\"\n              },\n              children: \"Solve\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 380,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 369,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this), output && /*#__PURE__*/_jsxDEV(\"textarea\", {\n          value: output,\n          readOnly: true,\n          style: {\n            width: \"100%\",\n            height: \"200px\",\n            marginTop: \"20px\",\n            fontFamily: \"monospace\",\n            padding: \"10px\"\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 277,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        borderLeft: \"1px solid #eee\",\n        padding: \"20px\",\n        height: \"100vh\",\n        overflowY: \"auto\",\n        position: \"sticky\",\n        top: 0,\n        backgroundColor: \"white\",\n        borderRadius: \"8px\",\n        boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\"\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        style: {\n          marginTop: 0\n        },\n        children: [\"Solutions Found: \", solutions.length]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 9\n      }, this), executionTime !== null && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: \"20px\",\n          color: \"#666\",\n          fontSize: \"14px\"\n        },\n        children: [\"Solution found in \", executionTime.toFixed(2), \"ms\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 425,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontFamily: \"monospace\",\n          whiteSpace: \"pre\"\n        },\n        children: solutions.map((solution, index) => {\n          // Create a map of position to cell state\n          const cellStates = new Map();\n          for (let i = 0; i < size; i++) {\n            for (let j = 0; j < size; j++) {\n              const cellRef = cellRefs.current[`${i}-${j}`];\n              if (cellRef) {\n                const state = cellRef.getState();\n                cellStates.set(`${i}-${j}`, state);\n              }\n            }\n          }\n\n          // Create a map of number to its cell state\n          const numberStates = new Map();\n          for (let i = 0; i < size; i++) {\n            for (let j = 0; j < size; j++) {\n              var _cellRefs$current;\n              const index = i * size + j + 1;\n              const state = (_cellRefs$current = cellRefs.current[`${i}-${j}`]) === null || _cellRefs$current === void 0 ? void 0 : _cellRefs$current.getState();\n              if (state) {\n                numberStates.set(index, state);\n              }\n            }\n          }\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: \"20px\"\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"Solution \", index + 1, \":\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 468,\n              columnNumber: 17\n            }, this), solution.map((row, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n              children: row.map((cell, j) => {\n                const state = cellStates.get(`${i}-${j}`);\n                const index = i * size + j + 1;\n                const numberState = numberStates.get(cell > 0 ? cell : index);\n                let displayValue;\n                if (cell === -1) {\n                  displayValue = \"  X  \";\n                } else if (state !== null && state !== void 0 && state.marked) {\n                  var _numberStates$get;\n                  displayValue = /*#__PURE__*/_jsxDEV(\"span\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: \"24px\",\n                        color: ((_numberStates$get = numberStates.get(index)) === null || _numberStates$get === void 0 ? void 0 : _numberStates$get.color) || \"black\"\n                      },\n                      children: index\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 482,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: \"24px\"\n                      },\n                      children: \"\\u2022\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 486,\n                      columnNumber: 29\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      style: {\n                        fontSize: \"24px\"\n                      },\n                      children: state.power\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 487,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 27\n                  }, this);\n                } else {\n                  displayValue = `${index}${(state === null || state === void 0 ? void 0 : state.power) || ''}`;\n                }\n                const markedCell = solution.some((r, ri) => r.some((c, ci) => {\n                  var _cellStates$get;\n                  return c === cell && ((_cellStates$get = cellStates.get(`${ri}-${ci}`)) === null || _cellStates$get === void 0 ? void 0 : _cellStates$get.marked);\n                }));\n                return /*#__PURE__*/_jsxDEV(\"span\", {\n                  style: {\n                    display: \"inline-block\",\n                    width: \"100px\",\n                    textAlign: \"center\",\n                    color: cell === -1 ? \"gray\" : (numberState === null || numberState === void 0 ? void 0 : numberState.color) || \"black\",\n                    fontWeight: cell > 0 ? \"bold\" : \"normal\",\n                    fontSize: state !== null && state !== void 0 && state.marked ? \"36px\" : \"24px\",\n                    backgroundColor: (state === null || state === void 0 ? void 0 : state.color) === \"white\" ? \"#f0f0f0\" : \"transparent\"\n                  },\n                  children: displayValue\n                }, j, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 500,\n                  columnNumber: 25\n                }, this);\n              })\n            }, i, false, {\n              fileName: _jsxFileName,\n              lineNumber: 470,\n              columnNumber: 19\n            }, this))]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 467,\n            columnNumber: 15\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 435,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 410,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 266,\n    columnNumber: 5\n  }, this);\n};\n_s(Board, \"WG6inab4SVEYitsc27XV3XqAmTQ=\", false, function () {\n  return [useColor];\n});\n_c = Board;\nexport default Board;\nvar _c;\n$RefreshReg$(_c, \"Board\");", "map": {"version": 3, "names": ["React", "useRef", "Cell", "useColor", "getAllSolutions", "jsxDEV", "_jsxDEV", "COLORS", "colorButtonStyle", "color", "isActive", "backgroundColor", "width", "height", "border", "powerButtonStyle", "power", "fontSize", "display", "alignItems", "justifyContent", "Board", "size", "initialSize", "_s", "activeColor", "setActiveColor", "cellRefs", "output", "setOutput", "useState", "name", "setName", "setSize", "solutions", "setSolutions", "executionTime", "setExecutionTime", "findConnectedCells", "colorCells", "visited", "i", "j", "_cellRefs$current$key", "key", "cellState", "current", "getState", "getConnectedGroup", "group", "<PERSON><PERSON><PERSON><PERSON>", "stack", "length", "currI", "currJ", "pop", "_cellRefs$current$key2", "index", "marked", "push", "adjacent", "nextI", "nextJ", "<PERSON><PERSON><PERSON>", "groups", "handleExport", "banned<PERSON><PERSON><PERSON>", "a<PERSON>ower<PERSON>ells", "b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "c<PERSON><PERSON>er<PERSON>ells", "d<PERSON><PERSON>er<PERSON><PERSON>s", "aliveGroups", "_cellRefs$current$key3", "<PERSON><PERSON><PERSON><PERSON>", "filter", "_cellRefs$current$key4", "Math", "floor", "alert", "sort", "a", "b", "n", "JSON", "stringify", "handleSolve", "gameDef", "startTime", "performance", "now", "result", "endTime", "cells", "ref", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "gap", "padding", "children", "flex", "borderRadius", "boxShadow", "position", "marginBottom", "type", "value", "onChange", "e", "parseInt", "target", "textAlign", "className", "marginTop", "flexDirection", "map", "onClick", "lineHeight", "readOnly", "fontFamily", "borderLeft", "overflowY", "top", "toFixed", "whiteSpace", "solution", "cellStates", "Map", "cellRef", "state", "set", "numberStates", "_cellRefs$current", "row", "cell", "get", "numberState", "displayValue", "_numberStates$get", "marked<PERSON>ell", "some", "r", "ri", "c", "ci", "_cellStates$get", "fontWeight", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Puzzles/Builder/ColorZero/src/Board.tsx"], "sourcesContent": ["import React, { useRef } from \"react\";\nimport Cell from \"./Cell\";\nimport { useColor } from \"./contexts/ColorContext\";\nimport { getAllSolutions } from \"./solution\";\n\nconst COLORS = [\n  \"white\",\n  \"#ff0000\",\n  \"#00ff00\",\n  \"#0000ff\",\n  \"#ffff00\",\n  \"#00ffff\",\n  \"#ff00ff\",\n  \"gray\",\n];\n\nconst colorButtonStyle = (\n  color: string,\n  isActive: boolean\n): React.CSSProperties => ({\n  backgroundColor: color,\n  width: \"50px\",\n  height: \"50px\",\n  border: isActive ? \"3px solid black\" : \"1px solid gray\",\n});\n\nconst powerButtonStyle = (\n  power: string,\n  isActive: boolean\n): React.CSSProperties => ({\n  backgroundColor: \"white\",\n  width: \"50px\",\n  height: \"50px\",\n  border: isActive ? \"3px solid black\" : \"1px solid gray\",\n  fontSize: \"24px\",\n  display: \"flex\",\n  alignItems: \"center\",\n  justifyContent: \"center\",\n});\n\ninterface CellState {\n  color: string;\n  power: string;\n  markNumber: number; // 0 = not marked, 1-4 = marked with number\n}\n\nconst Board = ({ size: initialSize = 4 }) => {\n  const { activeColor, setActiveColor } = useColor();\n  const cellRefs = useRef<{ [key: string]: { getState: () => CellState } }>({});\n  const [output, setOutput] = React.useState(\"\");\n  const [name, setName] = React.useState(\"1\");\n  const [size, setSize] = React.useState(initialSize);\n  const [solutions, setSolutions] = React.useState<number[][][]>([]);\n  const [executionTime, setExecutionTime] = React.useState<number | null>(null);\n\n  const findConnectedCells = (color: string): number[][] => {\n    // Create a map of cell positions with this color\n    const colorCells: { [key: string]: boolean } = {};\n    const visited: { [key: string]: boolean } = {};\n\n    // Fill the map\n    for (let i = 0; i < size; i++) {\n      for (let j = 0; j < size; j++) {\n        const key = `${i}-${j}`;\n        const cellState = cellRefs.current[key]?.getState();\n        if (cellState && cellState.color === color) {\n          colorCells[key] = true;\n        }\n      }\n    }\n\n    const getConnectedGroup = (i: number, j: number): number[] => {\n      const group: number[] = [];\n      const markedCells: number[] = [];\n      const stack: [number, number][] = [[i, j]];\n\n      while (stack.length > 0) {\n        const [currI, currJ] = stack.pop()!;\n        const key = `${currI}-${currJ}`;\n\n        if (visited[key]) continue;\n        visited[key] = true;\n\n        if (colorCells[key]) {\n          const index = currI * size + currJ + 1;\n          const cellState = cellRefs.current[key]?.getState();\n\n          // If cell is marked with \".\", add to markedCells, otherwise add to regular group\n          if (cellState && cellState.marked) {\n            markedCells.push(index);\n          } else {\n            group.push(index);\n          }\n\n          // Check adjacent cells (up, down, left, right)\n          const adjacent = [\n            [currI - 1, currJ],\n            [currI + 1, currJ],\n            [currI, currJ - 1],\n            [currI, currJ + 1],\n          ];\n\n          for (const [nextI, nextJ] of adjacent) {\n            if (nextI >= 0 && nextI < size && nextJ >= 0 && nextJ < size) {\n              const nextKey = `${nextI}-${nextJ}`;\n              if (colorCells[nextKey] && !visited[nextKey]) {\n                stack.push([nextI, nextJ]);\n              }\n            }\n          }\n        }\n      }\n\n      // Combine arrays with marked cells first\n      return [...markedCells, ...group];\n    };\n\n    const groups: number[][] = [];\n\n    // Find all connected components\n    for (let i = 0; i < size; i++) {\n      for (let j = 0; j < size; j++) {\n        const key = `${i}-${j}`;\n        if (colorCells[key] && !visited[key]) {\n          const group = getConnectedGroup(i, j);\n          if (group.length > 0) {\n            groups.push(group);\n          }\n        }\n      }\n    }\n\n    return groups;\n  };\n\n  const handleExport = () => {\n    const bannedCells: number[] = [];\n    const aPowerCells: number[] = [];\n    const bPowerCells: number[] = [];\n    const cPowerCells: number[] = [];\n    const dPowerCells: number[] = [];\n    const aliveGroups: number[][] = [];\n\n    // Get banned and power cells\n    for (let i = 0; i < size; i++) {\n      for (let j = 0; j < size; j++) {\n        const key = `${i}-${j}`;\n        const cellState = cellRefs.current[key]?.getState();\n        if (cellState) {\n          const index = i * size + j + 1;\n\n          if (cellState.color === \"gray\") {\n            bannedCells.push(index);\n          }\n          if (cellState.power === \"A\") {\n            aPowerCells.push(index);\n          }\n          if (cellState.power === \"B\") {\n            bPowerCells.push(index);\n          }\n          if (cellState.power === \"C\") {\n            cPowerCells.push(index);\n          }\n          if (cellState.power === \"D\") {\n            dPowerCells.push(index);\n          }\n        }\n      }\n    }\n\n    // Get connected components for each color except white and gray\n    let isValid = true;\n    for (const color of COLORS) {\n      if (color !== \"white\" && color !== \"gray\") {\n        const groups = findConnectedCells(color);\n        // Check if each group has exactly one marked cell\n        for (const group of groups) {\n          const markedCells = group.filter((index) => {\n            const i = Math.floor((index - 1) / size);\n            const j = (index - 1) % size;\n            const key = `${i}-${j}`;\n            const cellState = cellRefs.current[key]?.getState();\n            return cellState?.marked;\n          });\n\n          if (markedCells.length !== 1) {\n            alert(`Invalid group: each connected group must have exactly one marked cell`);\n            isValid = false;\n            break;\n          }\n        }\n        if (!isValid) break;\n        aliveGroups.push(...groups);\n      }\n    }\n\n    if (!isValid) {\n      return null;\n    }\n\n    // Sort aliveGroups by length in descending order\n    aliveGroups.sort((a, b) => b.length - a.length);\n\n    // Create output object starting with \"n\" field\n    const output: any = {\n      n: name,\n      a: aliveGroups,\n    };\n\n    // Only add non-empty arrays to output\n    if (bannedCells.length > 0) {\n      output[\"b\"] = bannedCells;\n    }\n    if (aPowerCells.length > 0) {\n      output[\"A\"] = aPowerCells;\n    }\n    if (bPowerCells.length > 0) {\n      output[\"B\"] = bPowerCells;\n    }\n    if (cPowerCells.length > 0) {\n      output[\"C\"] = cPowerCells;\n    }\n    if (dPowerCells.length > 0) {\n      output[\"D\"] = dPowerCells;\n    }\n\n    setOutput(JSON.stringify(output));\n    return output;\n  };\n\n  const handleSolve = () => {\n    const gameDef = handleExport();\n    if (gameDef == null) {\n      return;\n    }\n\n    const startTime = performance.now();\n    const result = getAllSolutions(size, gameDef);\n    const endTime = performance.now();\n    const executionTime = endTime - startTime;\n\n    setExecutionTime(executionTime);\n    setSolutions(result || []);\n  };\n\n  const cells = [];\n  for (let i = 0; i < size; i++) {\n    for (let j = 0; j < size; j++) {\n      cells.push(\n        <Cell\n          i={i}\n          j={j}\n          size={size}\n          key={`${i}-${j}`}\n          ref={(ref) => {\n            if (ref) {\n              cellRefs.current[`${i}-${j}`] = ref;\n            }\n          }}\n        />\n      );\n    }\n  }\n\n  return (\n    <div style={{ display: \"flex\", gap: \"20px\", padding: \"20px\" }}>\n      {/* Main board area */}\n      <div\n        style={{\n          flex: \"1\",\n          backgroundColor: \"white\",\n          padding: \"20px\",\n          borderRadius: \"8px\",\n          boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n        }}\n      >\n        <div style={{ position: \"relative\" }}>\n          {/* Size input above the board */}\n          <div\n            style={{\n              marginBottom: \"20px\",\n              display: \"flex\",\n              justifyContent: \"center\",\n            }}\n          >\n            <input\n              type=\"number\"\n              value={size}\n              onChange={(e) => setSize(parseInt(e.target.value))}\n              style={{\n                width: \"50px\",\n                height: \"50px\",\n                textAlign: \"center\",\n                fontSize: \"24px\",\n              }}\n            />\n          </div>\n\n          <div\n            style={{\n              position: \"relative\",\n              width: size * 100,\n              height: size * 100,\n            }}\n          >\n            {cells}\n          </div>\n\n          <div\n            className=\"board-footer\"\n            style={{\n              marginTop: \"20px\",\n              display: \"flex\",\n              flexDirection: \"column\",\n              gap: \"10px\",\n              alignItems: \"flex-start\",\n            }}\n          >\n            {/* Name input */}\n            <div style={{ display: \"flex\", gap: \"10px\" }}>\n              <input\n                type=\"text\"\n                value={name}\n                onChange={(e) => setName(e.target.value)}\n                style={{\n                  width: \"50px\",\n                  height: \"50px\",\n                  textAlign: \"center\",\n                  fontSize: \"24px\",\n                }}\n              />\n            </div>\n\n            {/* Colors */}\n            <div style={{ display: \"flex\", gap: \"10px\" }}>\n              {COLORS.map((color) => (\n                <button\n                  key={color}\n                  onClick={() => setActiveColor(color)}\n                  style={colorButtonStyle(color, activeColor === color)}\n                />\n              ))}\n            </div>\n\n            {/* Powers and dot */}\n            <div style={{ display: \"flex\", gap: \"10px\" }}>\n              {[\"A\", \"B\", \"C\", \"D\"].map((power) => (\n                <button\n                  key={power}\n                  onClick={() => setActiveColor(power)}\n                  style={powerButtonStyle(power, activeColor === power)}\n                >\n                  {power}\n                </button>\n              ))}\n              <button\n                onClick={() => setActiveColor(\".\")}\n                style={{\n                  ...powerButtonStyle(\".\", activeColor === \".\"),\n                  fontSize: \"36px\",\n                  lineHeight: \"36px\",\n                }}\n              >\n                &bull;\n              </button>\n            </div>\n\n            {/* Export and Solve buttons */}\n            <div style={{ display: \"flex\", gap: \"10px\" }}>\n              <button\n                onClick={handleExport}\n                style={{\n                  ...powerButtonStyle(\"export\", false),\n                  width: \"auto\",\n                  padding: \"0 20px\",\n                }}\n              >\n                Export\n              </button>\n              <button\n                onClick={handleSolve}\n                style={{\n                  ...powerButtonStyle(\"solve\", false),\n                  width: \"auto\",\n                  padding: \"0 20px\",\n                }}\n              >\n                Solve\n              </button>\n            </div>\n          </div>\n\n          {output && (\n            <textarea\n              value={output}\n              readOnly\n              style={{\n                width: \"100%\",\n                height: \"200px\",\n                marginTop: \"20px\",\n                fontFamily: \"monospace\",\n                padding: \"10px\",\n              }}\n            />\n          )}\n        </div>\n      </div>\n\n      {/* Solutions panel */}\n      <div\n        style={{\n          borderLeft: \"1px solid #eee\",\n          padding: \"20px\",\n          height: \"100vh\",\n          overflowY: \"auto\",\n          position: \"sticky\",\n          top: 0,\n          backgroundColor: \"white\",\n          borderRadius: \"8px\",\n          boxShadow: \"0 2px 4px rgba(0,0,0,0.1)\",\n        }}\n      >\n        <h3 style={{ marginTop: 0 }}>Solutions Found: {solutions.length}</h3>\n        {executionTime !== null && (\n          <div\n            style={{\n              marginBottom: \"20px\",\n              color: \"#666\",\n              fontSize: \"14px\",\n            }}\n          >\n            Solution found in {executionTime.toFixed(2)}ms\n          </div>\n        )}\n        <div\n          style={{\n            fontFamily: \"monospace\",\n            whiteSpace: \"pre\",\n          }}\n        >\n          {solutions.map((solution, index) => {\n            // Create a map of position to cell state\n            const cellStates = new Map();\n            for (let i = 0; i < size; i++) {\n              for (let j = 0; j < size; j++) {\n                const cellRef = cellRefs.current[`${i}-${j}`];\n                if (cellRef) {\n                  const state = cellRef.getState();\n                  cellStates.set(`${i}-${j}`, state);\n                }\n              }\n            }\n\n            // Create a map of number to its cell state\n            const numberStates = new Map();\n            for (let i = 0; i < size; i++) {\n              for (let j = 0; j < size; j++) {\n                const index = i * size + j + 1;\n                const state = cellRefs.current[`${i}-${j}`]?.getState();\n                if (state) {\n                  numberStates.set(index, state);\n                }\n              }\n            }\n\n            return (\n              <div key={index} style={{ marginBottom: \"20px\" }}>\n                <div>Solution {index + 1}:</div>\n                {solution.map((row, i) => (\n                  <div key={i}>\n                    {row.map((cell, j) => {\n                      const state = cellStates.get(`${i}-${j}`);\n                      const index = i * size + j + 1;\n                      const numberState = numberStates.get(cell > 0 ? cell : index);\n                      let displayValue;\n                      \n                      if (cell === -1) {\n                        displayValue = \"  X  \";\n                      } else if (state?.marked) {\n                        displayValue = (\n                          <span>\n                            <span style={{ \n                              fontSize: \"24px\",\n                              color: numberStates.get(index)?.color || \"black\"\n                            }}>{index}</span>\n                            <span style={{ fontSize: \"24px\" }}>&bull;</span>\n                            <span style={{ fontSize: \"24px\" }}>{state.power}</span>\n                          </span>\n                        );\n                      } else {\n                        displayValue = `${index}${state?.power || ''}`;\n                      }\n\n                      const markedCell = solution.some((r, ri) => \n                        r.some((c, ci) => c === cell && \n                          cellStates.get(`${ri}-${ci}`)?.marked)\n                      );\n                      \n                      return (\n                        <span \n                          key={j} \n                          style={{ \n                            display: \"inline-block\",\n                            width: \"100px\",\n                            textAlign: \"center\",\n                            color: cell === -1 ? \"gray\" : \n                                   numberState?.color || \"black\",\n                            fontWeight: cell > 0 ? \"bold\" : \"normal\",\n                            fontSize: state?.marked ? \"36px\" : \"24px\",\n                            backgroundColor: state?.color === \"white\" ? \"#f0f0f0\" : \"transparent\"\n                          }}\n                        >\n                          {displayValue}\n                        </span>\n                      );\n                    })}\n                  </div>\n                ))}\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Board;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC,OAAOC,IAAI,MAAM,QAAQ;AACzB,SAASC,QAAQ,QAAQ,yBAAyB;AAClD,SAASC,eAAe,QAAQ,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,MAAM,GAAG,CACb,OAAO,EACP,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,SAAS,EACT,MAAM,CACP;AAED,MAAMC,gBAAgB,GAAGA,CACvBC,KAAa,EACbC,QAAiB,MACQ;EACzBC,eAAe,EAAEF,KAAK;EACtBG,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,MAAM;EACdC,MAAM,EAAEJ,QAAQ,GAAG,iBAAiB,GAAG;AACzC,CAAC,CAAC;AAEF,MAAMK,gBAAgB,GAAGA,CACvBC,KAAa,EACbN,QAAiB,MACQ;EACzBC,eAAe,EAAE,OAAO;EACxBC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,MAAM;EACdC,MAAM,EAAEJ,QAAQ,GAAG,iBAAiB,GAAG,gBAAgB;EACvDO,QAAQ,EAAE,MAAM;EAChBC,OAAO,EAAE,MAAM;EACfC,UAAU,EAAE,QAAQ;EACpBC,cAAc,EAAE;AAClB,CAAC,CAAC;AAQF,MAAMC,KAAK,GAAGA,CAAC;EAAEC,IAAI,EAAEC,WAAW,GAAG;AAAE,CAAC,KAAK;EAAAC,EAAA;EAC3C,MAAM;IAAEC,WAAW;IAAEC;EAAe,CAAC,GAAGvB,QAAQ,CAAC,CAAC;EAClD,MAAMwB,QAAQ,GAAG1B,MAAM,CAAmD,CAAC,CAAC,CAAC;EAC7E,MAAM,CAAC2B,MAAM,EAAEC,SAAS,CAAC,GAAG7B,KAAK,CAAC8B,QAAQ,CAAC,EAAE,CAAC;EAC9C,MAAM,CAACC,IAAI,EAAEC,OAAO,CAAC,GAAGhC,KAAK,CAAC8B,QAAQ,CAAC,GAAG,CAAC;EAC3C,MAAM,CAACR,IAAI,EAAEW,OAAO,CAAC,GAAGjC,KAAK,CAAC8B,QAAQ,CAACP,WAAW,CAAC;EACnD,MAAM,CAACW,SAAS,EAAEC,YAAY,CAAC,GAAGnC,KAAK,CAAC8B,QAAQ,CAAe,EAAE,CAAC;EAClE,MAAM,CAACM,aAAa,EAAEC,gBAAgB,CAAC,GAAGrC,KAAK,CAAC8B,QAAQ,CAAgB,IAAI,CAAC;EAE7E,MAAMQ,kBAAkB,GAAI7B,KAAa,IAAiB;IACxD;IACA,MAAM8B,UAAsC,GAAG,CAAC,CAAC;IACjD,MAAMC,OAAmC,GAAG,CAAC,CAAC;;IAE9C;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,EAAEmB,CAAC,EAAE,EAAE;MAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,EAAEoB,CAAC,EAAE,EAAE;QAAA,IAAAC,qBAAA;QAC7B,MAAMC,GAAG,GAAG,GAAGH,CAAC,IAAIC,CAAC,EAAE;QACvB,MAAMG,SAAS,IAAAF,qBAAA,GAAGhB,QAAQ,CAACmB,OAAO,CAACF,GAAG,CAAC,cAAAD,qBAAA,uBAArBA,qBAAA,CAAuBI,QAAQ,CAAC,CAAC;QACnD,IAAIF,SAAS,IAAIA,SAAS,CAACpC,KAAK,KAAKA,KAAK,EAAE;UAC1C8B,UAAU,CAACK,GAAG,CAAC,GAAG,IAAI;QACxB;MACF;IACF;IAEA,MAAMI,iBAAiB,GAAGA,CAACP,CAAS,EAAEC,CAAS,KAAe;MAC5D,MAAMO,KAAe,GAAG,EAAE;MAC1B,MAAMC,WAAqB,GAAG,EAAE;MAChC,MAAMC,KAAyB,GAAG,CAAC,CAACV,CAAC,EAAEC,CAAC,CAAC,CAAC;MAE1C,OAAOS,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;QACvB,MAAM,CAACC,KAAK,EAAEC,KAAK,CAAC,GAAGH,KAAK,CAACI,GAAG,CAAC,CAAE;QACnC,MAAMX,GAAG,GAAG,GAAGS,KAAK,IAAIC,KAAK,EAAE;QAE/B,IAAId,OAAO,CAACI,GAAG,CAAC,EAAE;QAClBJ,OAAO,CAACI,GAAG,CAAC,GAAG,IAAI;QAEnB,IAAIL,UAAU,CAACK,GAAG,CAAC,EAAE;UAAA,IAAAY,sBAAA;UACnB,MAAMC,KAAK,GAAGJ,KAAK,GAAG/B,IAAI,GAAGgC,KAAK,GAAG,CAAC;UACtC,MAAMT,SAAS,IAAAW,sBAAA,GAAG7B,QAAQ,CAACmB,OAAO,CAACF,GAAG,CAAC,cAAAY,sBAAA,uBAArBA,sBAAA,CAAuBT,QAAQ,CAAC,CAAC;;UAEnD;UACA,IAAIF,SAAS,IAAIA,SAAS,CAACa,MAAM,EAAE;YACjCR,WAAW,CAACS,IAAI,CAACF,KAAK,CAAC;UACzB,CAAC,MAAM;YACLR,KAAK,CAACU,IAAI,CAACF,KAAK,CAAC;UACnB;;UAEA;UACA,MAAMG,QAAQ,GAAG,CACf,CAACP,KAAK,GAAG,CAAC,EAAEC,KAAK,CAAC,EAClB,CAACD,KAAK,GAAG,CAAC,EAAEC,KAAK,CAAC,EAClB,CAACD,KAAK,EAAEC,KAAK,GAAG,CAAC,CAAC,EAClB,CAACD,KAAK,EAAEC,KAAK,GAAG,CAAC,CAAC,CACnB;UAED,KAAK,MAAM,CAACO,KAAK,EAAEC,KAAK,CAAC,IAAIF,QAAQ,EAAE;YACrC,IAAIC,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGvC,IAAI,IAAIwC,KAAK,IAAI,CAAC,IAAIA,KAAK,GAAGxC,IAAI,EAAE;cAC5D,MAAMyC,OAAO,GAAG,GAAGF,KAAK,IAAIC,KAAK,EAAE;cACnC,IAAIvB,UAAU,CAACwB,OAAO,CAAC,IAAI,CAACvB,OAAO,CAACuB,OAAO,CAAC,EAAE;gBAC5CZ,KAAK,CAACQ,IAAI,CAAC,CAACE,KAAK,EAAEC,KAAK,CAAC,CAAC;cAC5B;YACF;UACF;QACF;MACF;;MAEA;MACA,OAAO,CAAC,GAAGZ,WAAW,EAAE,GAAGD,KAAK,CAAC;IACnC,CAAC;IAED,MAAMe,MAAkB,GAAG,EAAE;;IAE7B;IACA,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,EAAEmB,CAAC,EAAE,EAAE;MAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,EAAEoB,CAAC,EAAE,EAAE;QAC7B,MAAME,GAAG,GAAG,GAAGH,CAAC,IAAIC,CAAC,EAAE;QACvB,IAAIH,UAAU,CAACK,GAAG,CAAC,IAAI,CAACJ,OAAO,CAACI,GAAG,CAAC,EAAE;UACpC,MAAMK,KAAK,GAAGD,iBAAiB,CAACP,CAAC,EAAEC,CAAC,CAAC;UACrC,IAAIO,KAAK,CAACG,MAAM,GAAG,CAAC,EAAE;YACpBY,MAAM,CAACL,IAAI,CAACV,KAAK,CAAC;UACpB;QACF;MACF;IACF;IAEA,OAAOe,MAAM;EACf,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,WAAqB,GAAG,EAAE;IAChC,MAAMC,WAAqB,GAAG,EAAE;IAChC,MAAMC,WAAqB,GAAG,EAAE;IAChC,MAAMC,WAAqB,GAAG,EAAE;IAChC,MAAMC,WAAqB,GAAG,EAAE;IAChC,MAAMC,WAAuB,GAAG,EAAE;;IAElC;IACA,KAAK,IAAI9B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,EAAEmB,CAAC,EAAE,EAAE;MAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,EAAEoB,CAAC,EAAE,EAAE;QAAA,IAAA8B,sBAAA;QAC7B,MAAM5B,GAAG,GAAG,GAAGH,CAAC,IAAIC,CAAC,EAAE;QACvB,MAAMG,SAAS,IAAA2B,sBAAA,GAAG7C,QAAQ,CAACmB,OAAO,CAACF,GAAG,CAAC,cAAA4B,sBAAA,uBAArBA,sBAAA,CAAuBzB,QAAQ,CAAC,CAAC;QACnD,IAAIF,SAAS,EAAE;UACb,MAAMY,KAAK,GAAGhB,CAAC,GAAGnB,IAAI,GAAGoB,CAAC,GAAG,CAAC;UAE9B,IAAIG,SAAS,CAACpC,KAAK,KAAK,MAAM,EAAE;YAC9ByD,WAAW,CAACP,IAAI,CAACF,KAAK,CAAC;UACzB;UACA,IAAIZ,SAAS,CAAC7B,KAAK,KAAK,GAAG,EAAE;YAC3BmD,WAAW,CAACR,IAAI,CAACF,KAAK,CAAC;UACzB;UACA,IAAIZ,SAAS,CAAC7B,KAAK,KAAK,GAAG,EAAE;YAC3BoD,WAAW,CAACT,IAAI,CAACF,KAAK,CAAC;UACzB;UACA,IAAIZ,SAAS,CAAC7B,KAAK,KAAK,GAAG,EAAE;YAC3BqD,WAAW,CAACV,IAAI,CAACF,KAAK,CAAC;UACzB;UACA,IAAIZ,SAAS,CAAC7B,KAAK,KAAK,GAAG,EAAE;YAC3BsD,WAAW,CAACX,IAAI,CAACF,KAAK,CAAC;UACzB;QACF;MACF;IACF;;IAEA;IACA,IAAIgB,OAAO,GAAG,IAAI;IAClB,KAAK,MAAMhE,KAAK,IAAIF,MAAM,EAAE;MAC1B,IAAIE,KAAK,KAAK,OAAO,IAAIA,KAAK,KAAK,MAAM,EAAE;QACzC,MAAMuD,MAAM,GAAG1B,kBAAkB,CAAC7B,KAAK,CAAC;QACxC;QACA,KAAK,MAAMwC,KAAK,IAAIe,MAAM,EAAE;UAC1B,MAAMd,WAAW,GAAGD,KAAK,CAACyB,MAAM,CAAEjB,KAAK,IAAK;YAAA,IAAAkB,sBAAA;YAC1C,MAAMlC,CAAC,GAAGmC,IAAI,CAACC,KAAK,CAAC,CAACpB,KAAK,GAAG,CAAC,IAAInC,IAAI,CAAC;YACxC,MAAMoB,CAAC,GAAG,CAACe,KAAK,GAAG,CAAC,IAAInC,IAAI;YAC5B,MAAMsB,GAAG,GAAG,GAAGH,CAAC,IAAIC,CAAC,EAAE;YACvB,MAAMG,SAAS,IAAA8B,sBAAA,GAAGhD,QAAQ,CAACmB,OAAO,CAACF,GAAG,CAAC,cAAA+B,sBAAA,uBAArBA,sBAAA,CAAuB5B,QAAQ,CAAC,CAAC;YACnD,OAAOF,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAEa,MAAM;UAC1B,CAAC,CAAC;UAEF,IAAIR,WAAW,CAACE,MAAM,KAAK,CAAC,EAAE;YAC5B0B,KAAK,CAAC,uEAAuE,CAAC;YAC9EL,OAAO,GAAG,KAAK;YACf;UACF;QACF;QACA,IAAI,CAACA,OAAO,EAAE;QACdF,WAAW,CAACZ,IAAI,CAAC,GAAGK,MAAM,CAAC;MAC7B;IACF;IAEA,IAAI,CAACS,OAAO,EAAE;MACZ,OAAO,IAAI;IACb;;IAEA;IACAF,WAAW,CAACQ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKA,CAAC,CAAC7B,MAAM,GAAG4B,CAAC,CAAC5B,MAAM,CAAC;;IAE/C;IACA,MAAMxB,MAAW,GAAG;MAClBsD,CAAC,EAAEnD,IAAI;MACPiD,CAAC,EAAET;IACL,CAAC;;IAED;IACA,IAAIL,WAAW,CAACd,MAAM,GAAG,CAAC,EAAE;MAC1BxB,MAAM,CAAC,GAAG,CAAC,GAAGsC,WAAW;IAC3B;IACA,IAAIC,WAAW,CAACf,MAAM,GAAG,CAAC,EAAE;MAC1BxB,MAAM,CAAC,GAAG,CAAC,GAAGuC,WAAW;IAC3B;IACA,IAAIC,WAAW,CAAChB,MAAM,GAAG,CAAC,EAAE;MAC1BxB,MAAM,CAAC,GAAG,CAAC,GAAGwC,WAAW;IAC3B;IACA,IAAIC,WAAW,CAACjB,MAAM,GAAG,CAAC,EAAE;MAC1BxB,MAAM,CAAC,GAAG,CAAC,GAAGyC,WAAW;IAC3B;IACA,IAAIC,WAAW,CAAClB,MAAM,GAAG,CAAC,EAAE;MAC1BxB,MAAM,CAAC,GAAG,CAAC,GAAG0C,WAAW;IAC3B;IAEAzC,SAAS,CAACsD,IAAI,CAACC,SAAS,CAACxD,MAAM,CAAC,CAAC;IACjC,OAAOA,MAAM;EACf,CAAC;EAED,MAAMyD,WAAW,GAAGA,CAAA,KAAM;IACxB,MAAMC,OAAO,GAAGrB,YAAY,CAAC,CAAC;IAC9B,IAAIqB,OAAO,IAAI,IAAI,EAAE;MACnB;IACF;IAEA,MAAMC,SAAS,GAAGC,WAAW,CAACC,GAAG,CAAC,CAAC;IACnC,MAAMC,MAAM,GAAGtF,eAAe,CAACkB,IAAI,EAAEgE,OAAO,CAAC;IAC7C,MAAMK,OAAO,GAAGH,WAAW,CAACC,GAAG,CAAC,CAAC;IACjC,MAAMrD,aAAa,GAAGuD,OAAO,GAAGJ,SAAS;IAEzClD,gBAAgB,CAACD,aAAa,CAAC;IAC/BD,YAAY,CAACuD,MAAM,IAAI,EAAE,CAAC;EAC5B,CAAC;EAED,MAAME,KAAK,GAAG,EAAE;EAChB,KAAK,IAAInD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,EAAEmB,CAAC,EAAE,EAAE;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,EAAEoB,CAAC,EAAE,EAAE;MAC7BkD,KAAK,CAACjC,IAAI,cACRrD,OAAA,CAACJ,IAAI;QACHuC,CAAC,EAAEA,CAAE;QACLC,CAAC,EAAEA,CAAE;QACLpB,IAAI,EAAEA,IAAK;QAEXuE,GAAG,EAAGA,GAAG,IAAK;UACZ,IAAIA,GAAG,EAAE;YACPlE,QAAQ,CAACmB,OAAO,CAAC,GAAGL,CAAC,IAAIC,CAAC,EAAE,CAAC,GAAGmD,GAAG;UACrC;QACF;MAAE,GALG,GAAGpD,CAAC,IAAIC,CAAC,EAAE;QAAAoD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAMjB,CACH,CAAC;IACH;EACF;EAEA,oBACE3F,OAAA;IAAK4F,KAAK,EAAE;MAAEhF,OAAO,EAAE,MAAM;MAAEiF,GAAG,EAAE,MAAM;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAE5D/F,OAAA;MACE4F,KAAK,EAAE;QACLI,IAAI,EAAE,GAAG;QACT3F,eAAe,EAAE,OAAO;QACxByF,OAAO,EAAE,MAAM;QACfG,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE;MACb,CAAE;MAAAH,QAAA,eAEF/F,OAAA;QAAK4F,KAAK,EAAE;UAAEO,QAAQ,EAAE;QAAW,CAAE;QAAAJ,QAAA,gBAEnC/F,OAAA;UACE4F,KAAK,EAAE;YACLQ,YAAY,EAAE,MAAM;YACpBxF,OAAO,EAAE,MAAM;YACfE,cAAc,EAAE;UAClB,CAAE;UAAAiF,QAAA,eAEF/F,OAAA;YACEqG,IAAI,EAAC,QAAQ;YACbC,KAAK,EAAEtF,IAAK;YACZuF,QAAQ,EAAGC,CAAC,IAAK7E,OAAO,CAAC8E,QAAQ,CAACD,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAC,CAAE;YACnDV,KAAK,EAAE;cACLtF,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdoG,SAAS,EAAE,QAAQ;cACnBhG,QAAQ,EAAE;YACZ;UAAE;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAEN3F,OAAA;UACE4F,KAAK,EAAE;YACLO,QAAQ,EAAE,UAAU;YACpB7F,KAAK,EAAEU,IAAI,GAAG,GAAG;YACjBT,MAAM,EAAES,IAAI,GAAG;UACjB,CAAE;UAAA+E,QAAA,EAEDT;QAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3F,OAAA;UACE4G,SAAS,EAAC,cAAc;UACxBhB,KAAK,EAAE;YACLiB,SAAS,EAAE,MAAM;YACjBjG,OAAO,EAAE,MAAM;YACfkG,aAAa,EAAE,QAAQ;YACvBjB,GAAG,EAAE,MAAM;YACXhF,UAAU,EAAE;UACd,CAAE;UAAAkF,QAAA,gBAGF/F,OAAA;YAAK4F,KAAK,EAAE;cAAEhF,OAAO,EAAE,MAAM;cAAEiF,GAAG,EAAE;YAAO,CAAE;YAAAE,QAAA,eAC3C/F,OAAA;cACEqG,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE7E,IAAK;cACZ8E,QAAQ,EAAGC,CAAC,IAAK9E,OAAO,CAAC8E,CAAC,CAACE,MAAM,CAACJ,KAAK,CAAE;cACzCV,KAAK,EAAE;gBACLtF,KAAK,EAAE,MAAM;gBACbC,MAAM,EAAE,MAAM;gBACdoG,SAAS,EAAE,QAAQ;gBACnBhG,QAAQ,EAAE;cACZ;YAAE;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN3F,OAAA;YAAK4F,KAAK,EAAE;cAAEhF,OAAO,EAAE,MAAM;cAAEiF,GAAG,EAAE;YAAO,CAAE;YAAAE,QAAA,EAC1C9F,MAAM,CAAC8G,GAAG,CAAE5G,KAAK,iBAChBH,OAAA;cAEEgH,OAAO,EAAEA,CAAA,KAAM5F,cAAc,CAACjB,KAAK,CAAE;cACrCyF,KAAK,EAAE1F,gBAAgB,CAACC,KAAK,EAAEgB,WAAW,KAAKhB,KAAK;YAAE,GAFjDA,KAAK;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAGX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAGN3F,OAAA;YAAK4F,KAAK,EAAE;cAAEhF,OAAO,EAAE,MAAM;cAAEiF,GAAG,EAAE;YAAO,CAAE;YAAAE,QAAA,GAC1C,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAACgB,GAAG,CAAErG,KAAK,iBAC9BV,OAAA;cAEEgH,OAAO,EAAEA,CAAA,KAAM5F,cAAc,CAACV,KAAK,CAAE;cACrCkF,KAAK,EAAEnF,gBAAgB,CAACC,KAAK,EAAES,WAAW,KAAKT,KAAK,CAAE;cAAAqF,QAAA,EAErDrF;YAAK,GAJDA,KAAK;cAAA8E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKJ,CACT,CAAC,eACF3F,OAAA;cACEgH,OAAO,EAAEA,CAAA,KAAM5F,cAAc,CAAC,GAAG,CAAE;cACnCwE,KAAK,EAAE;gBACL,GAAGnF,gBAAgB,CAAC,GAAG,EAAEU,WAAW,KAAK,GAAG,CAAC;gBAC7CR,QAAQ,EAAE,MAAM;gBAChBsG,UAAU,EAAE;cACd,CAAE;cAAAlB,QAAA,EACH;YAED;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC,eAGN3F,OAAA;YAAK4F,KAAK,EAAE;cAAEhF,OAAO,EAAE,MAAM;cAAEiF,GAAG,EAAE;YAAO,CAAE;YAAAE,QAAA,gBAC3C/F,OAAA;cACEgH,OAAO,EAAErD,YAAa;cACtBiC,KAAK,EAAE;gBACL,GAAGnF,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC;gBACpCH,KAAK,EAAE,MAAM;gBACbwF,OAAO,EAAE;cACX,CAAE;cAAAC,QAAA,EACH;YAED;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3F,OAAA;cACEgH,OAAO,EAAEjC,WAAY;cACrBa,KAAK,EAAE;gBACL,GAAGnF,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC;gBACnCH,KAAK,EAAE,MAAM;gBACbwF,OAAO,EAAE;cACX,CAAE;cAAAC,QAAA,EACH;YAED;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELrE,MAAM,iBACLtB,OAAA;UACEsG,KAAK,EAAEhF,MAAO;UACd4F,QAAQ;UACRtB,KAAK,EAAE;YACLtF,KAAK,EAAE,MAAM;YACbC,MAAM,EAAE,OAAO;YACfsG,SAAS,EAAE,MAAM;YACjBM,UAAU,EAAE,WAAW;YACvBrB,OAAO,EAAE;UACX;QAAE;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN3F,OAAA;MACE4F,KAAK,EAAE;QACLwB,UAAU,EAAE,gBAAgB;QAC5BtB,OAAO,EAAE,MAAM;QACfvF,MAAM,EAAE,OAAO;QACf8G,SAAS,EAAE,MAAM;QACjBlB,QAAQ,EAAE,QAAQ;QAClBmB,GAAG,EAAE,CAAC;QACNjH,eAAe,EAAE,OAAO;QACxB4F,YAAY,EAAE,KAAK;QACnBC,SAAS,EAAE;MACb,CAAE;MAAAH,QAAA,gBAEF/F,OAAA;QAAI4F,KAAK,EAAE;UAAEiB,SAAS,EAAE;QAAE,CAAE;QAAAd,QAAA,GAAC,mBAAiB,EAACnE,SAAS,CAACkB,MAAM;MAAA;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,EACpE7D,aAAa,KAAK,IAAI,iBACrB9B,OAAA;QACE4F,KAAK,EAAE;UACLQ,YAAY,EAAE,MAAM;UACpBjG,KAAK,EAAE,MAAM;UACbQ,QAAQ,EAAE;QACZ,CAAE;QAAAoF,QAAA,GACH,oBACmB,EAACjE,aAAa,CAACyF,OAAO,CAAC,CAAC,CAAC,EAAC,IAC9C;MAAA;QAAA/B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CACN,eACD3F,OAAA;QACE4F,KAAK,EAAE;UACLuB,UAAU,EAAE,WAAW;UACvBK,UAAU,EAAE;QACd,CAAE;QAAAzB,QAAA,EAEDnE,SAAS,CAACmF,GAAG,CAAC,CAACU,QAAQ,EAAEtE,KAAK,KAAK;UAClC;UACA,MAAMuE,UAAU,GAAG,IAAIC,GAAG,CAAC,CAAC;UAC5B,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,EAAEmB,CAAC,EAAE,EAAE;YAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,EAAEoB,CAAC,EAAE,EAAE;cAC7B,MAAMwF,OAAO,GAAGvG,QAAQ,CAACmB,OAAO,CAAC,GAAGL,CAAC,IAAIC,CAAC,EAAE,CAAC;cAC7C,IAAIwF,OAAO,EAAE;gBACX,MAAMC,KAAK,GAAGD,OAAO,CAACnF,QAAQ,CAAC,CAAC;gBAChCiF,UAAU,CAACI,GAAG,CAAC,GAAG3F,CAAC,IAAIC,CAAC,EAAE,EAAEyF,KAAK,CAAC;cACpC;YACF;UACF;;UAEA;UACA,MAAME,YAAY,GAAG,IAAIJ,GAAG,CAAC,CAAC;UAC9B,KAAK,IAAIxF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGnB,IAAI,EAAEmB,CAAC,EAAE,EAAE;YAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGpB,IAAI,EAAEoB,CAAC,EAAE,EAAE;cAAA,IAAA4F,iBAAA;cAC7B,MAAM7E,KAAK,GAAGhB,CAAC,GAAGnB,IAAI,GAAGoB,CAAC,GAAG,CAAC;cAC9B,MAAMyF,KAAK,IAAAG,iBAAA,GAAG3G,QAAQ,CAACmB,OAAO,CAAC,GAAGL,CAAC,IAAIC,CAAC,EAAE,CAAC,cAAA4F,iBAAA,uBAA7BA,iBAAA,CAA+BvF,QAAQ,CAAC,CAAC;cACvD,IAAIoF,KAAK,EAAE;gBACTE,YAAY,CAACD,GAAG,CAAC3E,KAAK,EAAE0E,KAAK,CAAC;cAChC;YACF;UACF;UAEA,oBACE7H,OAAA;YAAiB4F,KAAK,EAAE;cAAEQ,YAAY,EAAE;YAAO,CAAE;YAAAL,QAAA,gBAC/C/F,OAAA;cAAA+F,QAAA,GAAK,WAAS,EAAC5C,KAAK,GAAG,CAAC,EAAC,GAAC;YAAA;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EAC/B8B,QAAQ,CAACV,GAAG,CAAC,CAACkB,GAAG,EAAE9F,CAAC,kBACnBnC,OAAA;cAAA+F,QAAA,EACGkC,GAAG,CAAClB,GAAG,CAAC,CAACmB,IAAI,EAAE9F,CAAC,KAAK;gBACpB,MAAMyF,KAAK,GAAGH,UAAU,CAACS,GAAG,CAAC,GAAGhG,CAAC,IAAIC,CAAC,EAAE,CAAC;gBACzC,MAAMe,KAAK,GAAGhB,CAAC,GAAGnB,IAAI,GAAGoB,CAAC,GAAG,CAAC;gBAC9B,MAAMgG,WAAW,GAAGL,YAAY,CAACI,GAAG,CAACD,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG/E,KAAK,CAAC;gBAC7D,IAAIkF,YAAY;gBAEhB,IAAIH,IAAI,KAAK,CAAC,CAAC,EAAE;kBACfG,YAAY,GAAG,OAAO;gBACxB,CAAC,MAAM,IAAIR,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEzE,MAAM,EAAE;kBAAA,IAAAkF,iBAAA;kBACxBD,YAAY,gBACVrI,OAAA;oBAAA+F,QAAA,gBACE/F,OAAA;sBAAM4F,KAAK,EAAE;wBACXjF,QAAQ,EAAE,MAAM;wBAChBR,KAAK,EAAE,EAAAmI,iBAAA,GAAAP,YAAY,CAACI,GAAG,CAAChF,KAAK,CAAC,cAAAmF,iBAAA,uBAAvBA,iBAAA,CAAyBnI,KAAK,KAAI;sBAC3C,CAAE;sBAAA4F,QAAA,EAAE5C;oBAAK;sBAAAqC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC,eACjB3F,OAAA;sBAAM4F,KAAK,EAAE;wBAAEjF,QAAQ,EAAE;sBAAO,CAAE;sBAAAoF,QAAA,EAAC;oBAAM;sBAAAP,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAChD3F,OAAA;sBAAM4F,KAAK,EAAE;wBAAEjF,QAAQ,EAAE;sBAAO,CAAE;sBAAAoF,QAAA,EAAE8B,KAAK,CAACnH;oBAAK;sBAAA8E,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CACP;gBACH,CAAC,MAAM;kBACL0C,YAAY,GAAG,GAAGlF,KAAK,GAAG,CAAA0E,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEnH,KAAK,KAAI,EAAE,EAAE;gBAChD;gBAEA,MAAM6H,UAAU,GAAGd,QAAQ,CAACe,IAAI,CAAC,CAACC,CAAC,EAAEC,EAAE,KACrCD,CAAC,CAACD,IAAI,CAAC,CAACG,CAAC,EAAEC,EAAE;kBAAA,IAAAC,eAAA;kBAAA,OAAKF,CAAC,KAAKT,IAAI,MAAAW,eAAA,GAC1BnB,UAAU,CAACS,GAAG,CAAC,GAAGO,EAAE,IAAIE,EAAE,EAAE,CAAC,cAAAC,eAAA,uBAA7BA,eAAA,CAA+BzF,MAAM;gBAAA,EACzC,CAAC;gBAED,oBACEpD,OAAA;kBAEE4F,KAAK,EAAE;oBACLhF,OAAO,EAAE,cAAc;oBACvBN,KAAK,EAAE,OAAO;oBACdqG,SAAS,EAAE,QAAQ;oBACnBxG,KAAK,EAAE+H,IAAI,KAAK,CAAC,CAAC,GAAG,MAAM,GACpB,CAAAE,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAEjI,KAAK,KAAI,OAAO;oBACpC2I,UAAU,EAAEZ,IAAI,GAAG,CAAC,GAAG,MAAM,GAAG,QAAQ;oBACxCvH,QAAQ,EAAEkH,KAAK,aAALA,KAAK,eAALA,KAAK,CAAEzE,MAAM,GAAG,MAAM,GAAG,MAAM;oBACzC/C,eAAe,EAAE,CAAAwH,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE1H,KAAK,MAAK,OAAO,GAAG,SAAS,GAAG;kBAC1D,CAAE;kBAAA4F,QAAA,EAEDsC;gBAAY,GAZRjG,CAAC;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAaF,CAAC;cAEX,CAAC;YAAC,GA9CMxD,CAAC;cAAAqD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OA+CN,CACN,CAAC;UAAA,GAnDMxC,KAAK;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAoDV,CAAC;QAEV,CAAC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzE,EAAA,CA/dIH,KAAK;EAAA,QAC+BlB,QAAQ;AAAA;AAAAkJ,EAAA,GAD5ChI,KAAK;AAieX,eAAeA,KAAK;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}