{"ast": null, "code": "import cloneDeep from \"lodash/cloneDeep\";\nimport uniqBy from \"lodash/uniqBy\";\n// import { readFileSync } from \"fs\";\n\nexport function getAllSolutions(size, game) {\n  var initBoard = [];\n  for (var i = 0; i < size; i++) {\n    initBoard[i] = [];\n    for (var j = 0; j < size; j++) {\n      var _game$b;\n      var index = i * size + j + 1;\n      if ((_game$b = game.b) !== null && _game$b !== void 0 && _game$b.includes(index)) {\n        initBoard[i][j] = -1;\n      } else {\n        initBoard[i][j] = 0;\n      }\n    }\n  }\n  game.a.forEach(alive => {\n    var index = alive[0] - 1;\n    var current = {\n      i: Math.floor(index / size),\n      j: Math.floor(index % size)\n    };\n    initBoard[current.i][current.j] = alive[0];\n  });\n  var results = [initBoard];\n  var visitedBoards = {};\n  [...game.a].reverse().forEach(alive => {\n    var newResults = [];\n    results.forEach(board => {\n      checkIsBoardVisited(size, board, visitedBoards);\n      var boards = getAllBlocks(alive[0], size, alive.length - 1, board, game, visitedBoards);\n      if (boards != null) {\n        newResults = newResults.concat(boards);\n      }\n    });\n    results = newResults;\n  });\n  return results;\n}\nfunction getAllBlocks(origin, size, remaining, board, game, visitedBoards) {\n  if (remaining === 0) {\n    var hasPower = checkPowers(origin, size, board, game);\n    if (hasPower) {\n      return [board];\n    }\n    return null;\n  }\n  var adjCells = getAllEmptyAdjCells(origin, size, board);\n  if (adjCells.length <= 0) {\n    return null;\n  }\n  var results = [];\n  adjCells.forEach(({\n    i,\n    j\n  }) => {\n    // if (\n    //   hasPower &&\n    //   (game[\"+\"]?.find((a) => a === i * size + j + 1) ||\n    //     game[\"-\"]?.find((a) => a === i * size + j + 1))\n    // ) {\n    //   return;\n    // }\n\n    var clone = cloneDeep(board);\n    clone[i][j] = origin;\n    if (checkIsBoardVisited(size, clone, visitedBoards)) {\n      return;\n    }\n    var b = getAllBlocks(origin, size, remaining - 1, clone, game, visitedBoards);\n    if (b != null) {\n      results = results.concat(b);\n    }\n  });\n  return results.length <= 0 ? null : results;\n}\nfunction getAllEmptyAdjCells(origin, size, board) {\n  var adjCells = [];\n  for (var i = 0; i < size; i++) {\n    for (var j = 0; j < size; j++) {\n      if (board[i][j] === origin) {\n        adjCells = adjCells.concat(getAdjCells({\n          i,\n          j\n        }, size, board));\n      }\n    }\n  }\n  return uniqBy(adjCells, cell => cell.i * size + cell.j);\n}\nfunction getAdjCells({\n  i,\n  j\n}, size, board) {\n  var adjCells = [];\n\n  // top\n  if (i > 0) {\n    if (board[i - 1][j] === 0) {\n      adjCells.push({\n        i: i - 1,\n        j\n      });\n    }\n  }\n\n  // bottom\n  if (i < size - 1) {\n    if (board[i + 1][j] === 0) {\n      adjCells.push({\n        i: i + 1,\n        j\n      });\n    }\n  }\n\n  // left\n  if (j > 0) {\n    if (board[i][j - 1] === 0) {\n      adjCells.push({\n        i,\n        j: j - 1\n      });\n    }\n  }\n\n  // right\n  if (j < size - 1) {\n    if (board[i][j + 1] === 0) {\n      adjCells.push({\n        i,\n        j: j + 1\n      });\n    }\n  }\n  return adjCells;\n}\nfunction hashBoard(size, board) {\n  var cells = [];\n  for (var i = 0; i < size; i++) {\n    for (var j = 0; j < size; j++) {\n      var val = board[i][j];\n      if (val > 0) {\n        cells.push(`${i * size + j}-${val}`);\n      }\n    }\n  }\n  return cells.join(\",\");\n}\nfunction checkIsBoardVisited(size, board, visitedBoards) {\n  var key = hashBoard(size, board);\n  if (visitedBoards[key]) {\n    return true;\n  }\n  visitedBoards[key] = true;\n  return false;\n}\nfunction checkPowers(origin, size, board, game) {\n  var positive = 0;\n  var negative = 0;\n  for (var i = 0; i < size; i++) {\n    for (var j = 0; j < size; j++) {\n      if (board[i][j] === origin) {\n        var _game$, _game$2;\n        if ((_game$ = game[\"+\"]) !== null && _game$ !== void 0 && _game$.find(a => a === i * size + j + 1)) {\n          positive++;\n          if (positive >= 1 && negative >= 1) {\n            return true;\n          }\n        } else if ((_game$2 = game[\"-\"]) !== null && _game$2 !== void 0 && _game$2.find(a => a === i * size + j + 1)) {\n          negative++;\n          if (positive >= 1 && negative >= 1) {\n            return true;\n          }\n        }\n      }\n    }\n  }\n  return false;\n}\n\n/*\n\n[\n  { name: '4-3', difficulty: 12.50 },\n  { name: '4-8', difficulty: 2 },\n  { name: '4-5', difficulty: 2.5 },\n  { name: '4-7', difficulty: 2.70 },\n  { name: '4-9', difficulty: 3.90 },\n  { name: '4-12', difficulty: 4.10 },\n  { name: '4-10', difficulty: 4.60 },\n  { name: '5-10', difficulty: 11.90 },\n  { name: '5-9', difficulty: 12.00 },\n  { name: '4-6', difficulty: 12.90 },\n  { name: '4-1', difficulty: 17.60 },\n  { name: '4-2', difficulty: 21.00 },\n  { name: '5-14', difficulty: 21.00 },\n  { name: '4-11', difficulty: 27.90 },\n  { name: '5-15', difficulty: 33.80 },\n  { name: '5-2', difficulty: 34.90 },\n  { name: '6-3', difficulty: 41.40 },\n  { name: '5-11', difficulty: 41.70 },\n  { name: '4-4', difficulty: 50.30 },\n  { name: '5-13', difficulty: 54.60 },\n  { name: '5-8', difficulty: 65.80 },\n  { name: '5-16', difficulty: 80.80 },\n  { name: '6-10', difficulty: 262.60 },\n  { name: '5-6', difficulty: 116.10 },\n  { name: '5-7', difficulty: 121.10 },\n  { name: '7-2', difficulty: 199.20 },\n  { name: '5-3', difficulty: 305.40 },\n  { name: '5-1', difficulty: 307.20 },\n  { name: '5-4', difficulty: 318 },\n  { name: '5-12', difficulty: 359.50 },\n  { name: '5-17', difficulty: 363.70 },\n  { name: '6-13', difficulty: 507.10 },\n  { name: '6-9', difficulty: 660.90 },\n  { name: '6-8', difficulty: 1026.70 },\n  { name: '6-12', difficulty: 1486.00 },\n  { name: '6-6', difficulty: 1507.60 },\n  { name: '7-5', difficulty: 1674.20 },\n  { name: '7-6', difficulty: 1899.10 },\n  { name: '6-2', difficulty: 2888.60 },\n  { name: '6-7', difficulty: 3074.00 },\n  { name: '6-11', difficulty: 5019.80 },\n  { name: '5-5', difficulty: 5599.50 },\n  { name: '7-7', difficulty: 7929.40 },\n  { name: '6-5', difficulty: 10918.70 },\n  { name: '6-1', difficulty: 17811.10 },\n  { name: '6-4', difficulty: 25663.30 },\n  { name: '7-8', difficulty: 25963.10 },\n  { name: '8-3', difficulty: 32021.40 },\n  { name: '7-4', difficulty: 32680.60 },\n  { name: '7-9', difficulty: 33851.70 },\n  { name: '8-2', difficulty: 34573.80 },\n  { name: '8-1', difficulty: 36681.50 },\n  { name: '7-10', difficulty: 53780.40 },\n  { name: '7-3', difficulty: 62138.90 },\n  { name: '8-4', difficulty: 71844.80 },\n  { name: '8-5', difficulty: 91609.80 },\n  { name: '7-1', difficulty: ? }\n]\n\n*/", "map": {"version": 3, "names": ["cloneDeep", "uniqBy", "getAllSolutions", "size", "game", "initBoard", "i", "j", "_game$b", "index", "b", "includes", "a", "for<PERSON>ach", "alive", "current", "Math", "floor", "results", "visitedBoards", "reverse", "newResults", "board", "checkIsBoardVisited", "boards", "getAllBlocks", "length", "concat", "origin", "remaining", "<PERSON><PERSON><PERSON><PERSON>", "checkPowers", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getAllEmptyAdjCells", "clone", "get<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cell", "push", "hashBoard", "cells", "val", "join", "key", "positive", "negative", "_game$", "_game$2", "find"], "sources": ["/Users/<USER>/Puzzles/Builder/ColorZero/src/solution.ts"], "sourcesContent": ["import cloneDeep from \"lodash/cloneDeep\";\nimport uniqBy from \"lodash/uniqBy\";\n// import { readFileSync } from \"fs\";\n\ninterface IGameDef {\n  n: string;\n  a: number[][];\n  b?: number[];\n  \"+\"?: number[];\n  \"-\"?: number[];\n}\n\ninterface IPosition {\n  i: number;\n  j: number;\n}\n\ntype Board = number[][];\n\nexport function getAllSolutions(size: number, game: IGameDef) {\n  var initBoard: number[][] = [];\n\n  for (var i = 0; i < size; i++) {\n    initBoard[i] = [];\n    for (var j = 0; j < size; j++) {\n      var index = i * size + j + 1;\n      if (game.b?.includes(index)) {\n        initBoard[i][j] = -1;\n      } else {\n        initBoard[i][j] = 0;\n      }\n    }\n  }\n\n  game.a.forEach((alive) => {\n    var index = alive[0] - 1;\n    var current = {\n      i: Math.floor(index / size),\n      j: Math.floor(index % size),\n    };\n\n    initBoard[current.i][current.j] = alive[0];\n  });\n\n  var results: Board[] = [initBoard];\n\n  var visitedBoards: { [Key: string]: boolean } = {};\n\n  [...game.a].reverse().forEach((alive) => {\n    var newResults: Board[] = [];\n\n    results.forEach((board) => {\n      checkIsBoardVisited(size, board, visitedBoards);\n\n      var boards = getAllBlocks(\n        alive[0],\n        size,\n        alive.length - 1,\n        board,\n        game,\n        visitedBoards\n      );\n      if (boards != null) {\n        newResults = newResults.concat(boards);\n      }\n    });\n\n    results = newResults;\n  });\n\n  return results;\n}\n\nfunction getAllBlocks(\n  origin: number,\n  size: number,\n  remaining: number,\n  board: Board,\n  game: IGameDef,\n  visitedBoards: { [Key: string]: boolean }\n): Board[] | null {\n  if (remaining === 0) {\n    var hasPower = checkPowers(origin, size, board, game);\n    if (hasPower) {\n      return [board];\n    }\n\n    return null;\n  }\n\n  var adjCells = getAllEmptyAdjCells(origin, size, board);\n\n  if (adjCells.length <= 0) {\n    return null;\n  }\n\n  var results: Board[] = [];\n  adjCells.forEach(({ i, j }) => {\n    // if (\n    //   hasPower &&\n    //   (game[\"+\"]?.find((a) => a === i * size + j + 1) ||\n    //     game[\"-\"]?.find((a) => a === i * size + j + 1))\n    // ) {\n    //   return;\n    // }\n\n    var clone = cloneDeep(board);\n\n    clone[i][j] = origin;\n    if (checkIsBoardVisited(size, clone, visitedBoards)) {\n      return;\n    }\n\n    var b = getAllBlocks(\n      origin,\n      size,\n      remaining - 1,\n      clone,\n      game,\n      visitedBoards\n    );\n\n    if (b != null) {\n      results = results.concat(b);\n    }\n  });\n\n  return results.length <= 0 ? null : results;\n}\n\nfunction getAllEmptyAdjCells(\n  origin: number,\n  size: number,\n  board: Board\n): IPosition[] {\n  var adjCells: IPosition[] = [];\n\n  for (var i = 0; i < size; i++) {\n    for (var j = 0; j < size; j++) {\n      if (board[i][j] === origin) {\n        adjCells = adjCells.concat(getAdjCells({ i, j }, size, board));\n      }\n    }\n  }\n\n  return uniqBy(adjCells, (cell) => cell.i * size + cell.j);\n}\n\nfunction getAdjCells({ i, j }: IPosition, size: number, board: Board) {\n  var adjCells: IPosition[] = [];\n\n  // top\n  if (i > 0) {\n    if (board[i - 1][j] === 0) {\n      adjCells.push({ i: i - 1, j });\n    }\n  }\n\n  // bottom\n  if (i < size - 1) {\n    if (board[i + 1][j] === 0) {\n      adjCells.push({ i: i + 1, j });\n    }\n  }\n\n  // left\n  if (j > 0) {\n    if (board[i][j - 1] === 0) {\n      adjCells.push({ i, j: j - 1 });\n    }\n  }\n\n  // right\n  if (j < size - 1) {\n    if (board[i][j + 1] === 0) {\n      adjCells.push({ i, j: j + 1 });\n    }\n  }\n\n  return adjCells;\n}\n\nfunction hashBoard(size: number, board: Board): string {\n  var cells: string[] = [];\n  for (var i = 0; i < size; i++) {\n    for (var j = 0; j < size; j++) {\n      var val = board[i][j];\n      if (val > 0) {\n        cells.push(`${i * size + j}-${val}`);\n      }\n    }\n  }\n\n  return cells.join(\",\");\n}\n\nfunction checkIsBoardVisited(\n  size: number,\n  board: Board,\n  visitedBoards: { [x: string]: boolean }\n): boolean {\n  var key = hashBoard(size, board);\n  if (visitedBoards[key]) {\n    return true;\n  }\n\n  visitedBoards[key] = true;\n  return false;\n}\n\nfunction checkPowers(\n  origin: number,\n  size: number,\n  board: Board,\n  game: IGameDef\n): boolean {\n  var positive = 0;\n  var negative = 0;\n\n  for (var i = 0; i < size; i++) {\n    for (var j = 0; j < size; j++) {\n      if (board[i][j] === origin) {\n        if (game[\"+\"]?.find((a) => a === i * size + j + 1)) {\n          positive++;\n          if (positive >= 1 && negative >= 1) {\n            return true;\n          }\n        } else if (game[\"-\"]?.find((a) => a === i * size + j + 1)) {\n          negative++;\n          if (positive >= 1 && negative >= 1) {\n            return true;\n          }\n        }\n      }\n    }\n  }\n\n  return false;\n}\n\n/*\n\n[\n  { name: '4-3', difficulty: 12.50 },\n  { name: '4-8', difficulty: 2 },\n  { name: '4-5', difficulty: 2.5 },\n  { name: '4-7', difficulty: 2.70 },\n  { name: '4-9', difficulty: 3.90 },\n  { name: '4-12', difficulty: 4.10 },\n  { name: '4-10', difficulty: 4.60 },\n  { name: '5-10', difficulty: 11.90 },\n  { name: '5-9', difficulty: 12.00 },\n  { name: '4-6', difficulty: 12.90 },\n  { name: '4-1', difficulty: 17.60 },\n  { name: '4-2', difficulty: 21.00 },\n  { name: '5-14', difficulty: 21.00 },\n  { name: '4-11', difficulty: 27.90 },\n  { name: '5-15', difficulty: 33.80 },\n  { name: '5-2', difficulty: 34.90 },\n  { name: '6-3', difficulty: 41.40 },\n  { name: '5-11', difficulty: 41.70 },\n  { name: '4-4', difficulty: 50.30 },\n  { name: '5-13', difficulty: 54.60 },\n  { name: '5-8', difficulty: 65.80 },\n  { name: '5-16', difficulty: 80.80 },\n  { name: '6-10', difficulty: 262.60 },\n  { name: '5-6', difficulty: 116.10 },\n  { name: '5-7', difficulty: 121.10 },\n  { name: '7-2', difficulty: 199.20 },\n  { name: '5-3', difficulty: 305.40 },\n  { name: '5-1', difficulty: 307.20 },\n  { name: '5-4', difficulty: 318 },\n  { name: '5-12', difficulty: 359.50 },\n  { name: '5-17', difficulty: 363.70 },\n  { name: '6-13', difficulty: 507.10 },\n  { name: '6-9', difficulty: 660.90 },\n  { name: '6-8', difficulty: 1026.70 },\n  { name: '6-12', difficulty: 1486.00 },\n  { name: '6-6', difficulty: 1507.60 },\n  { name: '7-5', difficulty: 1674.20 },\n  { name: '7-6', difficulty: 1899.10 },\n  { name: '6-2', difficulty: 2888.60 },\n  { name: '6-7', difficulty: 3074.00 },\n  { name: '6-11', difficulty: 5019.80 },\n  { name: '5-5', difficulty: 5599.50 },\n  { name: '7-7', difficulty: 7929.40 },\n  { name: '6-5', difficulty: 10918.70 },\n  { name: '6-1', difficulty: 17811.10 },\n  { name: '6-4', difficulty: 25663.30 },\n  { name: '7-8', difficulty: 25963.10 },\n  { name: '8-3', difficulty: 32021.40 },\n  { name: '7-4', difficulty: 32680.60 },\n  { name: '7-9', difficulty: 33851.70 },\n  { name: '8-2', difficulty: 34573.80 },\n  { name: '8-1', difficulty: 36681.50 },\n  { name: '7-10', difficulty: 53780.40 },\n  { name: '7-3', difficulty: 62138.90 },\n  { name: '8-4', difficulty: 71844.80 },\n  { name: '8-5', difficulty: 91609.80 },\n  { name: '7-1', difficulty: ? }\n]\n\n*/\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,kBAAkB;AACxC,OAAOC,MAAM,MAAM,eAAe;AAClC;;AAiBA,OAAO,SAASC,eAAeA,CAACC,IAAY,EAAEC,IAAc,EAAE;EAC5D,IAAIC,SAAqB,GAAG,EAAE;EAE9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,EAAEG,CAAC,EAAE,EAAE;IAC7BD,SAAS,CAACC,CAAC,CAAC,GAAG,EAAE;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,EAAEI,CAAC,EAAE,EAAE;MAAA,IAAAC,OAAA;MAC7B,IAAIC,KAAK,GAAGH,CAAC,GAAGH,IAAI,GAAGI,CAAC,GAAG,CAAC;MAC5B,KAAAC,OAAA,GAAIJ,IAAI,CAACM,CAAC,cAAAF,OAAA,eAANA,OAAA,CAAQG,QAAQ,CAACF,KAAK,CAAC,EAAE;QAC3BJ,SAAS,CAACC,CAAC,CAAC,CAACC,CAAC,CAAC,GAAG,CAAC,CAAC;MACtB,CAAC,MAAM;QACLF,SAAS,CAACC,CAAC,CAAC,CAACC,CAAC,CAAC,GAAG,CAAC;MACrB;IACF;EACF;EAEAH,IAAI,CAACQ,CAAC,CAACC,OAAO,CAAEC,KAAK,IAAK;IACxB,IAAIL,KAAK,GAAGK,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;IACxB,IAAIC,OAAO,GAAG;MACZT,CAAC,EAAEU,IAAI,CAACC,KAAK,CAACR,KAAK,GAAGN,IAAI,CAAC;MAC3BI,CAAC,EAAES,IAAI,CAACC,KAAK,CAACR,KAAK,GAAGN,IAAI;IAC5B,CAAC;IAEDE,SAAS,CAACU,OAAO,CAACT,CAAC,CAAC,CAACS,OAAO,CAACR,CAAC,CAAC,GAAGO,KAAK,CAAC,CAAC,CAAC;EAC5C,CAAC,CAAC;EAEF,IAAII,OAAgB,GAAG,CAACb,SAAS,CAAC;EAElC,IAAIc,aAAyC,GAAG,CAAC,CAAC;EAElD,CAAC,GAAGf,IAAI,CAACQ,CAAC,CAAC,CAACQ,OAAO,CAAC,CAAC,CAACP,OAAO,CAAEC,KAAK,IAAK;IACvC,IAAIO,UAAmB,GAAG,EAAE;IAE5BH,OAAO,CAACL,OAAO,CAAES,KAAK,IAAK;MACzBC,mBAAmB,CAACpB,IAAI,EAAEmB,KAAK,EAAEH,aAAa,CAAC;MAE/C,IAAIK,MAAM,GAAGC,YAAY,CACvBX,KAAK,CAAC,CAAC,CAAC,EACRX,IAAI,EACJW,KAAK,CAACY,MAAM,GAAG,CAAC,EAChBJ,KAAK,EACLlB,IAAI,EACJe,aACF,CAAC;MACD,IAAIK,MAAM,IAAI,IAAI,EAAE;QAClBH,UAAU,GAAGA,UAAU,CAACM,MAAM,CAACH,MAAM,CAAC;MACxC;IACF,CAAC,CAAC;IAEFN,OAAO,GAAGG,UAAU;EACtB,CAAC,CAAC;EAEF,OAAOH,OAAO;AAChB;AAEA,SAASO,YAAYA,CACnBG,MAAc,EACdzB,IAAY,EACZ0B,SAAiB,EACjBP,KAAY,EACZlB,IAAc,EACde,aAAyC,EACzB;EAChB,IAAIU,SAAS,KAAK,CAAC,EAAE;IACnB,IAAIC,QAAQ,GAAGC,WAAW,CAACH,MAAM,EAAEzB,IAAI,EAAEmB,KAAK,EAAElB,IAAI,CAAC;IACrD,IAAI0B,QAAQ,EAAE;MACZ,OAAO,CAACR,KAAK,CAAC;IAChB;IAEA,OAAO,IAAI;EACb;EAEA,IAAIU,QAAQ,GAAGC,mBAAmB,CAACL,MAAM,EAAEzB,IAAI,EAAEmB,KAAK,CAAC;EAEvD,IAAIU,QAAQ,CAACN,MAAM,IAAI,CAAC,EAAE;IACxB,OAAO,IAAI;EACb;EAEA,IAAIR,OAAgB,GAAG,EAAE;EACzBc,QAAQ,CAACnB,OAAO,CAAC,CAAC;IAAEP,CAAC;IAAEC;EAAE,CAAC,KAAK;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;;IAEA,IAAI2B,KAAK,GAAGlC,SAAS,CAACsB,KAAK,CAAC;IAE5BY,KAAK,CAAC5B,CAAC,CAAC,CAACC,CAAC,CAAC,GAAGqB,MAAM;IACpB,IAAIL,mBAAmB,CAACpB,IAAI,EAAE+B,KAAK,EAAEf,aAAa,CAAC,EAAE;MACnD;IACF;IAEA,IAAIT,CAAC,GAAGe,YAAY,CAClBG,MAAM,EACNzB,IAAI,EACJ0B,SAAS,GAAG,CAAC,EACbK,KAAK,EACL9B,IAAI,EACJe,aACF,CAAC;IAED,IAAIT,CAAC,IAAI,IAAI,EAAE;MACbQ,OAAO,GAAGA,OAAO,CAACS,MAAM,CAACjB,CAAC,CAAC;IAC7B;EACF,CAAC,CAAC;EAEF,OAAOQ,OAAO,CAACQ,MAAM,IAAI,CAAC,GAAG,IAAI,GAAGR,OAAO;AAC7C;AAEA,SAASe,mBAAmBA,CAC1BL,MAAc,EACdzB,IAAY,EACZmB,KAAY,EACC;EACb,IAAIU,QAAqB,GAAG,EAAE;EAE9B,KAAK,IAAI1B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,EAAEG,CAAC,EAAE,EAAE;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,EAAEI,CAAC,EAAE,EAAE;MAC7B,IAAIe,KAAK,CAAChB,CAAC,CAAC,CAACC,CAAC,CAAC,KAAKqB,MAAM,EAAE;QAC1BI,QAAQ,GAAGA,QAAQ,CAACL,MAAM,CAACQ,WAAW,CAAC;UAAE7B,CAAC;UAAEC;QAAE,CAAC,EAAEJ,IAAI,EAAEmB,KAAK,CAAC,CAAC;MAChE;IACF;EACF;EAEA,OAAOrB,MAAM,CAAC+B,QAAQ,EAAGI,IAAI,IAAKA,IAAI,CAAC9B,CAAC,GAAGH,IAAI,GAAGiC,IAAI,CAAC7B,CAAC,CAAC;AAC3D;AAEA,SAAS4B,WAAWA,CAAC;EAAE7B,CAAC;EAAEC;AAAa,CAAC,EAAEJ,IAAY,EAAEmB,KAAY,EAAE;EACpE,IAAIU,QAAqB,GAAG,EAAE;;EAE9B;EACA,IAAI1B,CAAC,GAAG,CAAC,EAAE;IACT,IAAIgB,KAAK,CAAChB,CAAC,GAAG,CAAC,CAAC,CAACC,CAAC,CAAC,KAAK,CAAC,EAAE;MACzByB,QAAQ,CAACK,IAAI,CAAC;QAAE/B,CAAC,EAAEA,CAAC,GAAG,CAAC;QAAEC;MAAE,CAAC,CAAC;IAChC;EACF;;EAEA;EACA,IAAID,CAAC,GAAGH,IAAI,GAAG,CAAC,EAAE;IAChB,IAAImB,KAAK,CAAChB,CAAC,GAAG,CAAC,CAAC,CAACC,CAAC,CAAC,KAAK,CAAC,EAAE;MACzByB,QAAQ,CAACK,IAAI,CAAC;QAAE/B,CAAC,EAAEA,CAAC,GAAG,CAAC;QAAEC;MAAE,CAAC,CAAC;IAChC;EACF;;EAEA;EACA,IAAIA,CAAC,GAAG,CAAC,EAAE;IACT,IAAIe,KAAK,CAAChB,CAAC,CAAC,CAACC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;MACzByB,QAAQ,CAACK,IAAI,CAAC;QAAE/B,CAAC;QAAEC,CAAC,EAAEA,CAAC,GAAG;MAAE,CAAC,CAAC;IAChC;EACF;;EAEA;EACA,IAAIA,CAAC,GAAGJ,IAAI,GAAG,CAAC,EAAE;IAChB,IAAImB,KAAK,CAAChB,CAAC,CAAC,CAACC,CAAC,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;MACzByB,QAAQ,CAACK,IAAI,CAAC;QAAE/B,CAAC;QAAEC,CAAC,EAAEA,CAAC,GAAG;MAAE,CAAC,CAAC;IAChC;EACF;EAEA,OAAOyB,QAAQ;AACjB;AAEA,SAASM,SAASA,CAACnC,IAAY,EAAEmB,KAAY,EAAU;EACrD,IAAIiB,KAAe,GAAG,EAAE;EACxB,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,EAAEG,CAAC,EAAE,EAAE;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,EAAEI,CAAC,EAAE,EAAE;MAC7B,IAAIiC,GAAG,GAAGlB,KAAK,CAAChB,CAAC,CAAC,CAACC,CAAC,CAAC;MACrB,IAAIiC,GAAG,GAAG,CAAC,EAAE;QACXD,KAAK,CAACF,IAAI,CAAC,GAAG/B,CAAC,GAAGH,IAAI,GAAGI,CAAC,IAAIiC,GAAG,EAAE,CAAC;MACtC;IACF;EACF;EAEA,OAAOD,KAAK,CAACE,IAAI,CAAC,GAAG,CAAC;AACxB;AAEA,SAASlB,mBAAmBA,CAC1BpB,IAAY,EACZmB,KAAY,EACZH,aAAuC,EAC9B;EACT,IAAIuB,GAAG,GAAGJ,SAAS,CAACnC,IAAI,EAAEmB,KAAK,CAAC;EAChC,IAAIH,aAAa,CAACuB,GAAG,CAAC,EAAE;IACtB,OAAO,IAAI;EACb;EAEAvB,aAAa,CAACuB,GAAG,CAAC,GAAG,IAAI;EACzB,OAAO,KAAK;AACd;AAEA,SAASX,WAAWA,CAClBH,MAAc,EACdzB,IAAY,EACZmB,KAAY,EACZlB,IAAc,EACL;EACT,IAAIuC,QAAQ,GAAG,CAAC;EAChB,IAAIC,QAAQ,GAAG,CAAC;EAEhB,KAAK,IAAItC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,EAAEG,CAAC,EAAE,EAAE;IAC7B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,EAAEI,CAAC,EAAE,EAAE;MAC7B,IAAIe,KAAK,CAAChB,CAAC,CAAC,CAACC,CAAC,CAAC,KAAKqB,MAAM,EAAE;QAAA,IAAAiB,MAAA,EAAAC,OAAA;QAC1B,KAAAD,MAAA,GAAIzC,IAAI,CAAC,GAAG,CAAC,cAAAyC,MAAA,eAATA,MAAA,CAAWE,IAAI,CAAEnC,CAAC,IAAKA,CAAC,KAAKN,CAAC,GAAGH,IAAI,GAAGI,CAAC,GAAG,CAAC,CAAC,EAAE;UAClDoC,QAAQ,EAAE;UACV,IAAIA,QAAQ,IAAI,CAAC,IAAIC,QAAQ,IAAI,CAAC,EAAE;YAClC,OAAO,IAAI;UACb;QACF,CAAC,MAAM,KAAAE,OAAA,GAAI1C,IAAI,CAAC,GAAG,CAAC,cAAA0C,OAAA,eAATA,OAAA,CAAWC,IAAI,CAAEnC,CAAC,IAAKA,CAAC,KAAKN,CAAC,GAAGH,IAAI,GAAGI,CAAC,GAAG,CAAC,CAAC,EAAE;UACzDqC,QAAQ,EAAE;UACV,IAAID,QAAQ,IAAI,CAAC,IAAIC,QAAQ,IAAI,CAAC,EAAE;YAClC,OAAO,IAAI;UACb;QACF;MACF;IACF;EACF;EAEA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}