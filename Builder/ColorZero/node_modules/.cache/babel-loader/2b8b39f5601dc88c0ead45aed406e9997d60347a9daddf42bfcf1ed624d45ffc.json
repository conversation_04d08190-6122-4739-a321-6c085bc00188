{"ast": null, "code": "var isArray = require('./isArray'),\n  isKey = require('./_isKey'),\n  stringToPath = require('./_stringToPath'),\n  toString = require('./toString');\n\n/**\n * Casts `value` to a path array if it's not one.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {Object} [object] The object to query keys on.\n * @returns {Array} Returns the cast property path array.\n */\nfunction castPath(value, object) {\n  if (isArray(value)) {\n    return value;\n  }\n  return isKey(value, object) ? [value] : stringToPath(toString(value));\n}\nmodule.exports = castPath;", "map": {"version": 3, "names": ["isArray", "require", "is<PERSON>ey", "stringToPath", "toString", "<PERSON><PERSON><PERSON>", "value", "object", "module", "exports"], "sources": ["/Users/<USER>/Puzzles/Builder/ColorZero/node_modules/lodash/_castPath.js"], "sourcesContent": ["var isArray = require('./isArray'),\n    isKey = require('./_isKey'),\n    stringToPath = require('./_stringToPath'),\n    toString = require('./toString');\n\n/**\n * Casts `value` to a path array if it's not one.\n *\n * @private\n * @param {*} value The value to inspect.\n * @param {Object} [object] The object to query keys on.\n * @returns {Array} Returns the cast property path array.\n */\nfunction castPath(value, object) {\n  if (isArray(value)) {\n    return value;\n  }\n  return isKey(value, object) ? [value] : stringToPath(toString(value));\n}\n\nmodule.exports = castPath;\n"], "mappings": "AAAA,IAAIA,OAAO,GAAGC,OAAO,CAAC,WAAW,CAAC;EAC9BC,KAAK,GAAGD,OAAO,CAAC,UAAU,CAAC;EAC3BE,YAAY,GAAGF,OAAO,CAAC,iBAAiB,CAAC;EACzCG,QAAQ,GAAGH,OAAO,CAAC,YAAY,CAAC;;AAEpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,QAAQA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC/B,IAAIP,OAAO,CAACM,KAAK,CAAC,EAAE;IAClB,OAAOA,KAAK;EACd;EACA,OAAOJ,KAAK,CAACI,KAAK,EAAEC,MAAM,CAAC,GAAG,CAACD,KAAK,CAAC,GAAGH,YAAY,CAACC,QAAQ,CAACE,KAAK,CAAC,CAAC;AACvE;AAEAE,MAAM,CAACC,OAAO,GAAGJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}