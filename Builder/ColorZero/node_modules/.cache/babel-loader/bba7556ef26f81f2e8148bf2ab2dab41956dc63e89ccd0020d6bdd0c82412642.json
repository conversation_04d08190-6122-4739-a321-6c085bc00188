{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Puzzles/Builder/ColorZero/src/App.tsx\";\nimport React from \"react\";\nimport \"./App.css\";\nimport Board from \"./Board\";\nimport { ColorProvider } from \"./contexts/ColorContext\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    style: {\n      backgroundColor: '#f5f5f5'\n    },\n    children: /*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"App-header\",\n      style: {\n        backgroundColor: '#f5f5f5',\n        minHeight: '100vh'\n      },\n      children: /*#__PURE__*/_jsxDEV(ColorProvider, {\n        children: /*#__PURE__*/_jsxDEV(Board, {\n          size: 5\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 11,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 8,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "Board", "ColorProvider", "jsxDEV", "_jsxDEV", "App", "className", "style", "backgroundColor", "children", "minHeight", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Puzzles/Builder/ColorZero/src/App.tsx"], "sourcesContent": ["import React from \"react\";\nimport \"./App.css\";\nimport Board from \"./Board\";\nimport { ColorProvider } from \"./contexts/ColorContext\";\n\nfunction App() {\n  return (\n    <div className=\"App\" style={{ backgroundColor: '#f5f5f5' }}>\n      <header className=\"App-header\" style={{ backgroundColor: '#f5f5f5', minHeight: '100vh' }}>\n        <ColorProvider>\n          <Board size={5} />\n        </ColorProvider>\n      </header>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,WAAW;AAClB,OAAOC,KAAK,MAAM,SAAS;AAC3B,SAASC,aAAa,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA;IAAKE,SAAS,EAAC,KAAK;IAACC,KAAK,EAAE;MAAEC,eAAe,EAAE;IAAU,CAAE;IAAAC,QAAA,eACzDL,OAAA;MAAQE,SAAS,EAAC,YAAY;MAACC,KAAK,EAAE;QAAEC,eAAe,EAAE,SAAS;QAAEE,SAAS,EAAE;MAAQ,CAAE;MAAAD,QAAA,eACvFL,OAAA,CAACF,aAAa;QAAAO,QAAA,eACZL,OAAA,CAACH,KAAK;UAACU,IAAI,EAAE;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACC,EAAA,GAVQX,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}